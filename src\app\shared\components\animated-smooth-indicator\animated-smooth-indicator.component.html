<div class="smooth-indicator-container">
    <div class="smooth-indicator-viewport">
        <div class="smooth-wrapper"
             [ngStyle]="getViewportStyle()">
            <div class="smooth-indicator-track"
                 [ngStyle]="getContainerStyle()">
                @for (dot of visibleDots; track dot.index) {
                <div class="smooth-indicator-dot"
                     [ngStyle]="getDotStyle(dot)"
                     [class.active]="dot.isActive">
                </div>
                }
            </div>
        </div>
    </div>

</div>