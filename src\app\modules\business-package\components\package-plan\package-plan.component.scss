.plan-card {
    width: 100%;
    display: flex;
    padding: 24px;
    margin-bottom: 20px;
    border-radius: 6px;
    background-color: #efefef;
    flex-direction: column;
    direction: rtl;
    text-align: right;
}

.badge-wrapper {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.badge {
  background: #FAAF40;
  border-radius: 3px;
  padding: 2px 12px;
  color: white; 
  font-size: 12px;
  font-weight: 600;
}
/******************/
.plan-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}
.price-section {
  display: flex;
  direction: rtl;
  flex-direction: column;
}
.price {
  color: #722282; 
  font-weight: 800;
  font-size: 24px;
  line-height: 28px;
}

.price-unit {
  font-size: 12px;
  font-weight: 600;
  margin-right: 4px;
}

.plan-title {
  color: #722282;
  font-size: 14px;
  font-weight: 700;
}

.package-details {
  text-align: right;
  color: #272728;
  font-size: 14px;
}

.item {
  margin-bottom: 8px;
}

.label {
  font-weight: 700;
}
.value {
  font-weight: 400;
  display: inline;
}
.dot-icon {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #272728;
  margin-left: 6px;
}
ul{
  list-style: none;
}
.renewal-note {
  width: 100%;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
}
  .note-text {
    font-size: 10px;
    font-weight: 700;
    color: #7C7C7C;
    line-height: 28px;
    text-align: right;
  }

  .note-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }