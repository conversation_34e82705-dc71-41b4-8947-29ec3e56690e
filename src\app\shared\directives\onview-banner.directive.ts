import { Directive, ElementRef, inject, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { ConversionEvents } from '../constants/conversion-events';
import { BannerModel } from '../models/common.model';
import { CommonService } from '../services/common.service';


@Directive({
    selector: '[onviewbanner]',
    standalone: true
})
export class OnviewBannerDirective implements OnInit, OnDestroy {
    @Input('onviewbanner') bannerItem: BannerModel;

    browser = inject(BrowserService);

    commonService = inject(CommonService);


    private observer: IntersectionObserver;
    private hasBeenViewed = false;
    private clickHandler: (event: Event) => void;

    constructor(private el: ElementRef) {
        this.clickHandler = this.onClick.bind(this);
    }

    ngOnInit(): void {
        this.setupIntersectionObserver();
        this.setupClickListener();
    }

    ngOnDestroy(): void {
        if (this.observer) {
            this.observer.disconnect();
        }
        this.el.nativeElement.removeEventListener('click', this.clickHandler);
    }

    private setupIntersectionObserver(): void {
        const options: IntersectionObserverInit = {
            root: null,
            rootMargin: '0px',
            threshold: 0.5
        };

        if (this.browser.isBrowser()) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.hasBeenViewed) {
                        this.hasBeenViewed = true;
                        this.sendViewEvent();
                    }
                });
            }, options);

            this.observer.observe(this.el.nativeElement);

        }

    }

    private setupClickListener(): void {
        this.el.nativeElement.addEventListener('click', this.clickHandler);
    }

    private onClick(event: Event): void {
        this.sendClickEvent(event);
    }

    private sendViewEvent(): void {
        if (!this.bannerItem || !this.bannerItem.isAd) return;

        if (!this.browser.isBrowser()) {
            return;
        }
        this.commonService.pushDataLayer({
            event: ConversionEvents.ADS_IMPRESSION,
            ads_sponsore_id: this.bannerItem.sponsorAdID,
        });
    }

    private sendClickEvent(event: Event): void {
        if (!this.bannerItem) return;

        if (!this.browser.isBrowser()) {
            return;
        }
        this.commonService.pushDataLayer({
            event: ConversionEvents.ADS_CLICK,
            ads_sponsore_id: this.bannerItem.sponsorAdID,
        });

    }

}
