import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CalendarComponent } from './calendar.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';

const meta: Meta<CalendarComponent> = {
    title: 'shared/Calendar',
    component: CalendarComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, FormsModule, CalendarModule],
        }),
    ],
    argTypes: {
        title: { control: 'text' },
        showButtons: { control: 'boolean' },
        selectedDateRangeChange: { action: 'selectedDateRangeChange' },
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<CalendarComponent>;

export const Default: Story = {
    args: {
        title: 'اختر التاريخ',
        showButtons: true,
        selectedDateRange: [],
    },
};

export const WithoutButtons: Story = {
    args: {
        title: 'التقويم',
        showButtons: false,
        selectedDateRange: [],
    },
};

export const PreselectedWeek: Story = {
    args: {
        title: 'الأسبوع الحالي',
        showButtons: true,
        selectedDateRange: (() => {
            const today = new Date();
            const dayOfWeek = today.getDay();
            const diffToMonday = (dayOfWeek + 6) % 7;
            const start = new Date(today);
            start.setDate(today.getDate() - diffToMonday);
            const end = new Date(start);
            end.setDate(start.getDate() + 6);
            const format = (d: Date) =>
                `${String(d.getDate()).padStart(2, '0')}/${String(d.getMonth() + 1).padStart(2, '0')}/${String(d.getFullYear()).slice(-2)}`;
            return [format(start), format(end)];
        })(),
    },
};
