@import "variables";


.ps-card {
    background: $warning-2;
    border-radius: 12px;
    padding: 12px 14px;
    inline-size: 100%;
    box-sizing: border-box;
    color: $text-muted-2;
    display: grid;
    gap: 8px;
}

.ps-header {
    font-weight: 700;
    color: $text-color;
}

.ps-row {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 10px;
}

.ps-count {
    font-size: 12px;
    color: $text-muted-2;
    white-space: nowrap;

    &.over {
        color: $warning;
        font-weight: 700;
    }
}

.ps-warning-text {
    margin-inline-start: 4px;
}

.ps-bar-wrap {
    position: relative;
    block-size: 8px;
    border-radius: 999px;
    overflow: hidden;
}

.ps-bar-bg {
    position: absolute;
    inset: 0;
    background: lighten($warning-2, 6%);
}

.ps-bar-fill {
    position: absolute;
    inset-block: 0;
    inset-inline-start: 0;
    background: $orangeColor;

    &.over {
        background: $warning;
    }
}

.ps-bar-will-fill {
    position: absolute;
    inset-block: 0;
    inset-inline-start: 0;
    background: lighten($orangeColor, 20%);

    &.over {
        background: $warning;
    }
}

.ps-footer {
    font-size: 12px;
    color: $text-muted-1;
}

.ps-details {
    display: grid;
    gap: 4px;
    font-size: 14px;

    strong {
        font-weight: 700;
    }
}