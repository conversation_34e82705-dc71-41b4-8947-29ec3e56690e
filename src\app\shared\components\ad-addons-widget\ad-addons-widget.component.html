<div [ngClass]="{'promo-card': !reviewMode}">
    <div class="header">
        <div class="title-wrap">
            <app-svg-icons *ngIf="addon.iconId" [name]="addon.iconId" width="25px" color="#722282"
                height="25px"></app-svg-icons>
            <span class="title" *ngIf="addon.title">
                {{ addon.title }}
            </span>
        </div>
    </div>

    <p class="desc" *ngIf="addon.description">{{ addon.description }}</p>

    <div class="meta">
        <div class="row" *ngIf="addon.validityValue">
            <span class="label">الصلاحية: <span class="value">{{ addon.validityValue }}</span></span>

        </div>
        <div class="row" *ngIf="addon.costValue">
            <span class="label">التكلفة: <span class="value">{{ addon.costValue }}</span></span>
        </div>
    </div>


    <ng-container *ngIf="!reviewMode">

        <button type="button" class="read-more" (click)="onReadMore()" *ngIf="showReadMore">
            {{ "Showmore" | translate}} <span class="arrow" aria-hidden="true">
                <app-svg-icons name="back-icon" width="12px" height="12px"></app-svg-icons>
            </span>
        </button>

        <div *ngIf="!addon.subscribed; else notSubscribedTemp">
            <app-shared-btn [label]="('add' | translate)" (btnClick)="onAdd()" [disabled]="disabled"></app-shared-btn>
        </div>

        <ng-template #notSubscribedTemp>
            <div class="auto-renew">
                <span>
                    تتجدد تلقائيًا بعد <strong>{{ addon.daysLeft }} أيام</strong>
                </span>
            </div>
            <app-shared-btn label="تمديد المدة" (btnClick)="onAdd()" [disabled]="disabled"
                theme="bgWhite"></app-shared-btn>
        </ng-template>

    </ng-container>

</div>