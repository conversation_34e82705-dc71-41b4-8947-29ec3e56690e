<form [formGroup]="form"
      autocomplete="off"
      #target>
  <div class="form_fields">


    <!-- Input Group -->
    <div>
      <ng-container *ngFor="let prop of categoryProps">
        <ng-container [ngSwitch]="prop.type">
          <ng-container *ngSwitchCase="propType.TextBox">
            <span [attr.data-id]="prop.id"
                  class="inputField field"
                  [ngClass]="{'required_input' : prop.isMandatory && hasError(prop.name , 'required')}"
                  [hidden]="!shouldShowProperty(prop)">
              <input pInputText
                     [id]="prop.name"
                     [formControlName]="prop.name"
                     [placeholder]="prop.name | translate" />
              <b *ngIf="prop.isMandatory && hasError(prop.name , 'required')"
                 class="required">*</b>
            </span>
          </ng-container>

        </ng-container>
      </ng-container>
    </div>


    <!-- Number Group -->
    <ng-container *ngFor="let prop of categoryProps"
                  class="field">
      <ng-container [ngSwitch]="prop.type">

        <ng-container *ngSwitchCase="propType.Number">
          <span [attr.data-id]="prop.id"
                class="inputField field"
                [ngClass]="{'required_input' : prop.isMandatory && hasError(prop.name , 'required')}"
                [hidden]="!shouldShowProperty(prop)">
            <input type="text"
                   appArabicToEnglishNumerals
                   pInputText
                   [id]="prop.name"
                   [formControlName]="prop.name"
                   [placeholder]="prop.name | translate" />
            <b *ngIf="prop.isMandatory && hasError(prop.name , 'required')"
               class="required">*</b>
          </span>
        </ng-container>
        <div *ngSwitchDefault></div>
      </ng-container>
    </ng-container>



    <!-- Dropdown Group -->
    <div>
      <ng-container *ngFor="let prop of categoryProps">
        <ng-container [ngSwitch]="prop.type">
          <ng-container *ngSwitchCase="propType.Dropdown">
            <div [attr.data-id]="prop.id"
                 class="dropInput field"
                 [hidden]="!shouldShowProperty(prop)">

              <p-dropdown [ngClass]="{'required_drop' : prop.isMandatory && hasError(prop.name , 'required')}"
                          [options]="prop.values | parseprops : getSelectedParent(prop.parentId)"
                          [formControlName]="prop.name"
                          (onChange)="onChangeItem($event,prop , prop.name)"
                          optionLabel="viewValue"
                          optionValue="value"
                          [virtualScroll]="true"
                          filter="true"
                          [emptyFilterMessage]="'no_result_found' | translate"
                          [virtualScrollItemSize]="44"
                          [placeholder]="prop.name | translate"
                          [required]="prop.isMandatory && hasError(prop.name , 'required')"
                          filterBy="viewValue">

                <ng-template let-item
                             pTemplate="selectedItem">

                  <div class="flex align-items-center gap-2 selectDrop">

                    <div>
                      {{ item.viewValue }}
                    </div>
                  </div>
                </ng-template>
                <ng-template let-item
                             pTemplate="item">
                  <div class="flex align-items-center gap-2">
                    <div>{{ item.viewValue }}</div>
                  </div>
                </ng-template>
              </p-dropdown>
            </div>
          </ng-container>
          <div *ngSwitchDefault></div>
        </ng-container>
      </ng-container>
    </div>




    <!-- Range Group -->
    <div>
      <ng-container *ngFor="let prop of categoryProps">
        <ng-container [ngSwitch]="prop.type">
          <ng-container *ngSwitchCase="propType.Range">
            <span [attr.data-id]="prop.id"
                  class="rangeField field"
                  [ngClass]="{'required_input' : prop.isMandatory && hasError(prop.name , 'required')}"
                  [hidden]="!shouldShowProperty(prop)">
              <h2>{{ prop.name | translate }} <b *ngIf="prop.isMandatory && hasError(prop.name , 'required')"
                   class="required">*</b></h2>
              <app-range-input [formControlName]="prop.name"></app-range-input>
            </span>
          </ng-container>
          <div *ngSwitchDefault></div>
        </ng-container>
      </ng-container>
    </div>





    <!-- Boolean Group -->
    <div class="boolean_holder"
         *ngIf="categoryProps | includeBooleans">
      <ng-container *ngFor="let prop of categoryProps">
        <ng-container [ngSwitch]="prop.type">
          <ng-container *ngSwitchCase="propType.Boolean">
            <div [attr.data-id]="prop.id"
                 class="switchInput field"
                 [ngClass]="{'required_input' : prop.isMandatory && hasError(prop.name , 'required')}"
                 [hidden]="!shouldShowProperty(prop)">
              <span>{{ prop.name | translate }} <b *ngIf="prop.isMandatory && hasError(prop.name , 'required')"
                   class="required">*</b></span>
              <p-inputSwitch (onChange)="onChangeItem($event,prop.id , prop.name)"
                             [formControlName]="prop.name"></p-inputSwitch>
            </div>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>

    <!-- Radio Group -->
    <div>
      <ng-container *ngFor="let prop of categoryProps">
        <ng-container [ngSwitch]="prop.type">
          <ng-container *ngSwitchCase="propType.RadioButton">
            <div [attr.data-id]="prop.id"
                 class="toggleInput"
                 [ngClass]="{'required_input' : prop.isMandatory && hasError(prop.name , 'required')}"
                 [hidden]="!shouldShowProperty(prop)">
              <h2>{{ prop.name | translate }} <b *ngIf="prop.isMandatory && hasError(prop.name , 'required')"
                   class="required">*</b></h2>

              <div class="toggleList radio_items">
                <ng-container *ngFor="let item of prop.values; let i = index">
                  <div class="radio_item">
                    <p-radioButton [inputId]="prop.name + '_' + i"
                                   [value]="item"
                                   [formControlName]="prop.name"
                                   (onClick)="onChangeItem({value: item}, prop, prop.name)"></p-radioButton>
                    <label [for]="prop.name + '_' + i"
                           class="ml-2">{{ item | translate }}</label>
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-container>
          <div *ngSwitchDefault></div>
        </ng-container>
      </ng-container>
    </div>



  </div>

</form>