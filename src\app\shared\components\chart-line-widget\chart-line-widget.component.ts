import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { Component, Input, SimpleChanges, ViewChild } from '@angular/core';
import { NgApexchartsModule } from 'ng-apexcharts';
import type {
  ApexAxisChartSeries, ApexChart, ApexStroke, ApexMarkers,
  ApexXAxis, ApexYAxis, ApexGrid, ApexDataLabels, ApexTitleSubtitle, ApexTooltip,
  ChartComponent
} from 'ng-apexcharts';
@Component({
  selector: 'app-chart-line-widget',
  standalone: true,
  imports: [NgApexchartsModule],
  templateUrl: './chart-line-widget.component.html',
  styleUrl: './chart-line-widget.component.scss'
})
export class ChartLineWidgetComponent {

  @Input() data: number[] = [1, 2, 3, 4, 5, 6, 7];
  @Input() title = 'إجمالي الرسائل';

  labels: string[] = ['10/2', '11/2', '12/2', '13/2', '14/2', '15/2', '16/2'];
  isRtl = true;

  series: ApexAxisChartSeries = [{ name: 'الرسائل', data: [] }];

  colors = ['#8F4FB3'];

  chart: ApexChart = {
    type: 'line',
    height: 230,
    toolbar: { show: false },
    zoom: { enabled: false },
  };

  stroke: ApexStroke = { width: 3, curve: 'straight' };

  xaxis: ApexXAxis = {
    categories: [],
    labels: { style: { fontSize: '12px' } },
    axisBorder: { show: false },
    axisTicks: { show: false }
  };

  yaxis: ApexYAxis = {
    opposite: true,
    min: 0, max: 60, tickAmount: 6,
    labels: {
      formatter: (v: number) => (v === 0 ? '0' : `${v}k`)
    }
  };

  grid: ApexGrid = {
    borderColor: '#e9ecef', strokeDashArray: 3,
    yaxis: { lines: { show: true } },
    xaxis: { lines: { show: false } }
  };

  dataLabels: ApexDataLabels = { enabled: false };
  tooltip: ApexTooltip = { enabled: false };

  ngOnChanges(): void {

    if (this.browserService.isBrowser()) {
      this.isRtl = document.documentElement.dir === 'rtl';
      this.applyDir();

      const cats = this.isRtl ? [...this.labels].reverse() : [...this.labels];
      const vals = this.isRtl ? [...this.data].reverse() : [...this.data];

      this.xaxis = { ...this.xaxis, categories: cats };
      this.series = [{ name: 'الرسائل', data: vals }];
    }

  }

  constructor(private browserService: BrowserService) { }

  private applyDir() {
    this.chart = { ...this.chart };
    this.yaxis = { ...this.yaxis, opposite: this.isRtl };
  }
}
