import { <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { StatsBlockComponent } from '../../components/stats-block/stats-block.component';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { FilterPanelComponent } from '../../components/filter-panel/filter-panel.component';
import { ChartLineWidgetComponent } from "@src/app/shared/components/chart-line-widget/chart-line-widget.component";
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
import { AdsAndAddOnsSheetCycleComponent } from '../../ads-and-add-ons-sheet-cycle/ads-and-add-ons-sheet-cycle.component';
import { environment } from '@src/environments/environment';
import { Router } from '@angular/router';
import { title } from 'process';

@Component({
  selector: 'app-ad-detials-page',
  standalone: true,
  imports: [StatsBlockComponent, NgFor, NtranslatePipe, DynamicHeaderComponent, ChartLineWidgetComponent, SvgIconsComponent, SharedBtnComponent],
  templateUrl: './ad-detials-page.component.html',
  styleUrl: './ad-detials-page.component.scss'
})
export class AdDetialsPageComponent {

  dashboardStats = [
    { label: "total_messages", value: 231, icon: 'message-icon' },
    { label: "total_calls", value: 231, icon: 'phone-icon' },
    { label: "total_views", value: 231, icon: 'arrow-top-left' },
    { label: "total_offers", value: 231, icon: 'tag-icon' },
    { label: "total_impressions", value: 231, icon: 'eye-icon' },
  ];

  chartData = [
    { name: 'إجمالي المكالمات', data: [40, 30, 40, 50, 40, 30, 50] },
    { name: 'إجمالي الرسائل', data: [0, 10, 20, 30, 40, 50, 60] },
    { name: 'إجمالي المشاهدات', data: [60, 50, 40, 30, 20, 10, 0] },
    { name: 'إجمالي النقرات', data: [20, 40, 25, 15, 10, 40, 50] },
    { name: 'إجمالي العروض', data: [20, 40, 25, 15, 10, 40, 50] },
  ]

  ads = {
    id: 1,
    title: 'Redmi note 13 pro 4G',
    imageUrl: '../../../../../../assets/images/image 4.jpg',
    callCount: 200,
    viewCount: 32,
    appearanceCount: 103,
    tags: ['Top Featured'],
  }

  filtersValues = {
    sortOption: '',
    callRange: '',
    messageRange: '',
    title: 'اختار تاريخ الاحصائيات',
    selectedDateRange: [],
  }

  selectedAds = new Set<number>();

  constructor(private alertHandlerService: AlertHandlerService, private router: Router) { }

  openFilter() {
    this.alertHandlerService.DynamicDialogOpen<FilterPanelComponent>(FilterPanelComponent, this.filtersValues, (callbackData: any) => {
      if (callbackData) {
        this.filtersValues = callbackData;
      }
    })
  }

  openAddon() {
    this.alertHandlerService.DynamicDialogOpen<AdsAndAddOnsSheetCycleComponent>(AdsAndAddOnsSheetCycleComponent, this.filtersValues, (callbackData: any) => {
      if (callbackData) {
        this.filtersValues = callbackData;
      }
    })
  }

  backButton() {
    this.router.navigate(['/ads-and-add-ons/management']);
  }
}
