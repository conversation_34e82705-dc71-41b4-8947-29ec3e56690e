import { Component, EventEmitter, Input, Output, OnChanges, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { NgClass, NgIf } from '@angular/common';
import { NtranslatePipe } from '../../pipes/ntranslate.pipe';

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [CalendarModule, FormsModule, NgIf, NgClass, NtranslatePipe],
  templateUrl: './calendar.component.html',
  styleUrl: './calendar.component.scss'
})
export class CalendarComponent implements OnChanges {
  @Input() title = '';
  @Input() showButtons = true;

  @Input() selectedDateRange: string[] = [];
  @Output() selectedDateRangeChange = new EventEmitter<string[]>();

  internalDates: Date[] = [];

  selected: 'today' | 'week' | 'month' | undefined;

  ngOnChanges(changes: SimpleChanges): void {
    if ('selectedDateRange' in changes) {
      this.internalDates = this.stringsToDates(this.selectedDateRange);
    }
  }

  selectToday() {
    this.selected = 'today';
    const today = this.atMidnight(new Date());
    this.internalDates = [today, today];
    this.emitStrings();
  }

  selectThisWeek() {
    this.selected = 'week';
    const today = this.atMidnight(new Date());
    const dayOfWeek = today.getDay();
    const diffToMonday = (dayOfWeek + 6) % 7;
    const start = this.addDays(today, -diffToMonday);
    const end = this.addDays(start, 6);
    this.internalDates = [start, end];
    this.emitStrings();
  }

  selectThisMonth() {
    this.selected = 'month';
    const now = new Date();
    const start = this.atMidnight(new Date(now.getFullYear(), now.getMonth(), 1));
    const end = this.atMidnight(new Date(now.getFullYear(), now.getMonth() + 1, 0));
    this.internalDates = [start, end];
    this.emitStrings();
  }

  onDateSelected(_: any) {
    this.emitStrings();
  }

  private emitStrings() {
    this.selectedDateRangeChange.emit(this.datesToStrings(this.internalDates));
  }

  private stringsToDates(arr: string[] | null | undefined): Date[] {
    if (!arr || arr.length === 0) return [];
    const dates = arr
      .map(s => this.parseDMY(s))
      .filter((d): d is Date => !!d)
      .map(d => this.atMidnight(d));
    return dates.slice(0, 2);
  }

  private parseDMY(s: string): Date | null {
    const parts = s.trim().split(/[\/\-]/);
    if (parts.length !== 3) return null;
    const d = parseInt(parts[0], 10);
    const m = parseInt(parts[1], 10);
    const y = parseInt(parts[2], 10);
    if (!Number.isFinite(d) || !Number.isFinite(m) || !Number.isFinite(y)) return null;
    const dt = new Date(y, m - 1, d);
    // Validate round-trip (handles invalid like 31/02)
    if (dt.getFullYear() !== y || dt.getMonth() !== m - 1 || dt.getDate() !== d) return null;
    return dt;
  }

  private datesToStrings(arr: Date[] | null | undefined): string[] {
    if (!arr || arr.length === 0) return [];
    return arr
      .filter(Boolean)
      .map(d => this.formatDMY(d));
  }

  private formatDMY(d: Date): string {
    const day = d.getDate();
    const month = d.getMonth() + 1;
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  private atMidnight(d: Date): Date {
    const nd = new Date(d);
    nd.setHours(0, 0, 0, 0);
    return nd;
  }

  private addDays(d: Date, days: number): Date {
    const nd = new Date(d);
    nd.setDate(nd.getDate() + days);
    return this.atMidnight(nd);
  }
}
