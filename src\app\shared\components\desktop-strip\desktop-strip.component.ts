import { CommonModule, NgOptimizedImage } from '@angular/common';
import { Component, ElementRef, inject, Input, signal } from '@angular/core';
import { Store } from '@ngrx/store';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { getAds } from '@src/app/store/app/selectors/app.selector';
import { map, Observable, tap } from 'rxjs';
import { ConversionEvents } from '../../constants/conversion-events';
import { AdsSectionDTO, AdsSlotName } from '../../models/lookup.model';
import { AdsService } from '../../services/ads.service';
import { AppcenterService } from '../../services/appcenter.service';
import { CommonService } from '../../services/common.service';
import { LookupService } from '../../services/lookup.service';

@Component({
  selector: 'app-desktop-strip',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage],
  templateUrl: './desktop-strip.component.html',
  styleUrl: './desktop-strip.component.scss'
})
export class DesktopStripComponent {

  @Input() type: string = AdsSlotName.CategoryDesktopRegularStrip;

  @Input() keyName!: string;

  lookService = inject(LookupService);
  appService = inject(AppcenterService);
  store = inject(Store);


  browser = inject(BrowserService);

  commonservice = inject(CommonService);

  adsService = inject(AdsService);

  private elementRef: ElementRef = inject(ElementRef);

  private observer!: any;
  options = {
    root: null,
    rootMargin: '0px',
    threshold: 0.3
  };


  private adsContainer = signal(null as AdsSectionDTO | null);





  ads$: Observable<AdsSectionDTO | undefined> = this.store.select(getAds).pipe(
    map(res => res.find(item => item.name == this.type)), tap(res => {
      this.adsContainer.set(res);
      if (this.browser.isBrowser()) {
        this.observer = new IntersectionObserver(entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.sendEvent();
              this.observer.unobserve(this.elementRef.nativeElement);
            }
          });
        }, this.options);

        this.observer.observe(this.elementRef.nativeElement);

      }
    })
  )


  sendEvent() {
    if (!this.browser.isBrowser()) {
      return;
    }
    this.commonservice.pushDataLayer({
      event: ConversionEvents.ADS_IMPRESSION,
      ads_id: this.adsContainer()?.ads[0]?.id || '',
      ads_sponsore_id: this.adsContainer()?.ads[0]?.sponsorId,
      ads_location: this.adsContainer()?.locationName || '',
      ads_key: this.keyName || ''
    });
  }

  onAdClick(id: number) {
    if (!this.browser.isBrowser()) {
      return;
    }
    this.commonservice.pushDataLayer({
      event: ConversionEvents.ADS_CLICK,
      ads_id: this.adsContainer()?.ads[0]?.id || '',
      ads_sponsore_id: this.adsContainer()?.ads[0]?.sponsorId,
      ads_location: this.adsContainer()?.locationName || '',
      ads_key: this.keyName || ''
    });

    this.adsService.adsClick(this.adsContainer()?.ads[0]?.sponsorId).subscribe();
  }


}
