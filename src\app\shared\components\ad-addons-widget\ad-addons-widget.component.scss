@import "variables";

.promo-card {
    background: rgba(238, 238, 238, 1); // light grey background
    border-radius: 12px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    inline-size: 100%;
    box-sizing: border-box;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-wrap {
        display: flex;
        align-items: center;
        gap: 6px;

        .title {
            font-weight: 700;
            font-size: 16px;
            color: $primary; // purple from variables
            font-family: $f-b;
        }
    }
}

.desc {
    font-size: 14px;
    line-height: 1.5;
    color: $text-color;
    margin: 0;
    font-family: $f-r;
}

.meta {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .row {
        display: flex;
        gap: 4px;
        font-size: 14px;
        color: $text-color;

        .label {
            font-weight: 700;
        }

        .value {
            font-weight: 400;
        }
    }
}

.read-more {
    background: none;
    border: none;
    padding: 0;
    font-size: 14px;
    font-weight: 700;
    color: $primary;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;

    .arrow {
        font-weight: 700;
    }

    &:hover {
        text-decoration: underline;
    }
}

.cta {
    background: linear-gradient(180deg, lighten($primary, 10%), $primary);
    color: #fff;
    font-size: 16px;
    font-weight: 700;
    padding: 10px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    text-align: center;
    margin-top: auto;
    font-family: $f-b;

    &:disabled {
        background: $text-muted-3;
        cursor: not-allowed;
    }
}

.auto-renew {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    border-radius: 8px;
    background: rgba(217, 217, 217, 1);
    color: $text-color;
    font-weight: 500;
    font-size: 14px;
    font-family: $f-r;

    strong {
        font-weight: 800;
    }
}

::ng-deep app-svg-icons[name="back-icon"] svg {
    transform: rotate(var(--arrow-rotation));
}