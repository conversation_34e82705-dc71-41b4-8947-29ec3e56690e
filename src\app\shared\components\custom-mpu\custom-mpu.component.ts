import { Component, Input } from '@angular/core';
import { LazyloadDirective } from '../../directives/lazyload.directive';
import { AdsDTO } from '../../models/lookup.model';

@Component({
  selector: 'app-custom-mpu',
  standalone: true,
  imports: [LazyloadDirective],
  templateUrl: './custom-mpu.component.html',
  styleUrl: './custom-mpu.component.scss'
})
export class CustomMpuComponent {
  @Input() ads!: AdsDTO;

}
