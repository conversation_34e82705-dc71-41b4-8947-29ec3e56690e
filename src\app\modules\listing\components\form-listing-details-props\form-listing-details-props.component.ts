import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MenuItem } from 'primeng/api';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { RadioButtonModule } from 'primeng/radiobutton';
import { Subscription } from 'rxjs';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { ParsepropsPipe } from 'src/app/modules/listing/pipes/parseprops.pipe';
import { InnerListingService } from 'src/app/modules/listing/services/inner-listing.service';
import { RelatedPropsService } from 'src/app/modules/listing/services/related-props.service';
import { RangeInputComponent } from 'src/app/shared/components/range-input/range-input.component';
import { ArabicToEnglishNumeralsDirective } from 'src/app/shared/directives/arabictoenglishnum.directive';
import { ListingPropsFormPosition, SelectedProperty, TempSelectedProperty } from 'src/app/shared/models/listing.model';
import { CategoryAttributesDTO, PropertiesType } from "src/app/shared/models/lookup.model";
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { LookupService } from 'src/app/shared/services/lookup.service';
import { IncludeBooleansPipe } from '../../pipes/include-booleans.pipe';

@Component({
  selector: 'app-form-listing-details-props',
  standalone: true,
  imports: [CommonModule,
    ReactiveFormsModule,
    InputTextModule,
    InputNumberModule,
    DropdownModule,
    RadioButtonModule,
    InputSwitchModule,
    RangeInputComponent,
    ParsepropsPipe,
    NtranslatePipe,
    ArabicToEnglishNumeralsDirective,
    IncludeBooleansPipe
  ],
  templateUrl: './form-listing-details-props.component.html',
  styleUrls: ['./form-listing-details-props.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class FormListingDetailsPropsComponent implements OnInit, OnDestroy {
  @ViewChild('target') targetHost: ElementRef<HTMLInputElement>;
  @Input() mainCategory: MenuItem;
  @Input() subCategory: MenuItem;
  @Input() formPosition: ListingPropsFormPosition;
  @Input() props: SelectedProperty[]
  @Output() onSubmit = new EventEmitter();
  categoryProps: CategoryAttributesDTO[] = [];
  selectedProps: SelectedProperty[] = [];
  form: FormGroup;
  propType = PropertiesType;
  watcher: Subscription;
  watcherProps: Subscription;
  selectedParants = new Map<number, any>();
  adoptionKey = 'ForAdoption';

  constructor(
    private lookupService: LookupService,
    public innerService: InnerListingService,
    private selectedParentsService: RelatedPropsService,
    private element: ElementRef,
    private browser: BrowserService,
  ) { }

  ngOnInit(): void {


    this.form = new FormGroup({
    });

    if (this.subCategory) {
      this.getCategoryProp(+this.subCategory.id!);
    } else {
      this.getCategoryProp(+this.mainCategory.id!);
    }


    this.watcher = this.innerService.fetch.subscribe(res => {
      if (!res) return;
      this.saveData();
    });

    this.watcherProps = this.selectedParentsService.selectedParents$.subscribe(res => {
      this.selectedParants = res;
    });
  }

  ngOnDestroy(): void {
    this.watcher.unsubscribe();
    this.watcherProps.unsubscribe();
  }

  isParentSelected(pid) {
    return this.selectedParants.has(pid);
  }

  isParentValueMatched(parentId: number, parentPropertySelectedValue: string): boolean {
    if (!parentId || !parentPropertySelectedValue) return true;

    const parentProp = this.categoryProps.find(prop => prop.id === parentId);
    if (!parentProp) return false;

    const parentCurrentValue = this.form.get(parentProp.name)?.value;
    if (!parentCurrentValue) return false;

    const allowedValues = parentPropertySelectedValue.split(',').map(v => v.trim());
    return allowedValues.includes(parentCurrentValue);
  }

  shouldShowProperty(prop: any): boolean {
    if (!prop.parentId) return true;

    const isParentSelected = this.isParentSelected(prop.parentId);
    if (!isParentSelected) return false;

    if (!prop.parentPropertySelectedValue) return true;

    return this.isParentValueMatched(prop.parentId, prop.parentPropertySelectedValue);
  }

  hasError(controlName: string, errorName: string) {
    return this.form.controls[controlName].hasError(errorName);
  }

  getSelectedParent(id) {

    if (id) {
      if (this.selectedParants?.has(id)) {
        return this.form.get(this.selectedParants?.get(id).name)?.value;
      }
    }
  }

  onChangeItem(e, prop, name) {

    if (name == this.adoptionKey) {
      this.innerService.updateIsForAdoption(e.checked);
    }

    const currentProp = this.categoryProps.find(p => p.name === name);
    if (currentProp) {
      const hasChildren = this.categoryProps.some(p => p.parentId === currentProp.id);

      if (hasChildren) {
        if (e.value || e.checked) {
          this.selectedParentsService.setParent(currentProp.id, {
            ...currentProp,
            selectedValue: e.value || e.checked
          });
        } else {
          this.selectedParentsService.deleteParent(currentProp.id);
          this.clearDependentFields(currentProp.id);
        }
      }
    }

    if (e.value || e.checked) {
      if (typeof prop === 'number') {
        if (!this.selectedParentsService.hasParent(prop)) {
          const propObj = this.categoryProps.find(p => p.id === prop);
          if (propObj) {
            this.selectedParentsService.setParent(prop, {
              ...propObj,
              selectedValue: e.value || e.checked
            });
          }
        }
      } else if (prop && prop.id) {
        if (!this.selectedParentsService.hasParent(prop.id)) {
          this.selectedParentsService.setParent(prop.id, {
            ...prop,
            selectedValue: e.value || e.checked
          });
        }
      }
    } else {
      if (typeof prop === 'number') {
        this.selectedParentsService.deleteParent(prop);
        this.clearDependentFields(prop);
      } else if (prop && prop.id) {
        this.selectedParentsService.deleteParent(prop.id);
        this.clearDependentFields(prop.id);
      }
    }
  }

  clearDependentFields(parentId: number) {
    const dependentProps = this.categoryProps.filter(prop => prop.parentId === parentId);
    dependentProps.forEach(prop => {
      this.form.get(prop.name)?.setValue('');
      this.clearDependentFields(prop.id);
    });
  }

  saveData() {


    if (this.form.valid) {
      let items: SelectedProperty[] = [];
      let tempItems: TempSelectedProperty[] = [];
      for (let item in this.form.value) {
        let e = this.categoryProps.find(res => res.name == item);
        if (e) {
          const tid = this.props.find(pr => pr.catgoryPropertyID == e.id);
          let tempItemObj = { name: item, value: this.form.value[item], propTypeName: e.typeName, catgoryPropertyID: e.id };
          let itemObj = { catgoryPropertyID: e.id, value: this.form.value[item], name: item, propTypeName: e.typeName };

          if (tid && tid.id) {
            tempItemObj['id'] = tid.id;
            itemObj['id'] = tid.id;
          }
          tempItems.push(tempItemObj);
          items.push(itemObj);
        }
      }
      this.onSubmit.emit({ items, tempItems });
    } else {
      const required_input = this.element.nativeElement.querySelectorAll('.required_drop , .required_input');

      if (required_input && required_input.length > 0) {
        this.browser.scrollTo({
          behavior: 'smooth',
          top: required_input[0].offsetTop - 100,
        });
      } else {
        this.browser.scrollTo({
          behavior: 'smooth',
          top: this.targetHost.nativeElement.offsetTop - 100,
        });
      }



      this.innerService.hasPropsError = true;
      return;

    }
  }

  getBooleanList() {
    return [{ value: "true", viewValue: 'Yes' }, { value: "false", viewValue: 'No' }];
  }

  getCategoryProp(catID: number, data?: SelectedProperty[]) {
    this.selectedProps = [];
    this.categoryProps?.forEach((y) => {
      this.form.removeControl(y.name);
    });



    this.lookupService.getCategoryProperties(catID).subscribe((x) => {
      if (x.succeded) {

        this.categoryProps = x.data.filter(item => item.formPosition == this.formPosition);


        this.categoryProps?.forEach((element) => {
          element.values = element.values.sort();

          if (element.isMandatory)
            this.form.addControl(
              element.name,
              new FormControl('', [Validators.required])
            );
          else
            this.form.addControl(
              element.name,
              new FormControl('')
            );
        });


        if (this.props.length > 0) this.fillPropertiesData(this.props);
      }
    });
  }
  fillPropertiesData(data: SelectedProperty[]) {
    this.selectedProps = data;
    this.categoryProps?.forEach((element) => {
      let value = data.filter((x) => x.catgoryPropertyID === element.id)[0];
      if (value) {
        this.form.controls[element.name].setValue(
          value.value
        );

        if (element.parentId) {
          this.selectedParentsService.setParent(element.parentId, element);
        }
      }
    });
  }

}
