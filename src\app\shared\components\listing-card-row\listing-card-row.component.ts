import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component, Input } from '@angular/core';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { NtranslatePipe } from '../../pipes/ntranslate.pipe';
import { SharedBtnComponent } from '../shared-btn/shared-btn.component';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { Router } from '@angular/router';
import { AdsAndAddOnsSheetCycleComponent } from '@src/app/modules/ads-and-add-ons/ads-and-add-ons-sheet-cycle/ads-and-add-ons-sheet-cycle.component';

@Component({
  selector: 'app-listing-card-row',
  standalone: true,
  imports: [NgClass, NgIf, NgFor, SvgIconsComponent, NtranslatePipe, SharedBtnComponent],
  templateUrl: './listing-card-row.component.html',
  styleUrl: './listing-card-row.component.scss',
})
export class ListingCardRowComponent {

  @Input() isSelectedMode: boolean = false;
  @Input() isSelected: boolean = false;
  @Input() listingDetails: any = {};


  constructor(private alertHandlerService: AlertHandlerService, private router: Router) { }


  addSelectedAds() {
    this.alertHandlerService.DynamicDialogOpen<AdsAndAddOnsSheetCycleComponent>(AdsAndAddOnsSheetCycleComponent, {}, (callbackData: any) => {
      if (callbackData) {
        // this.filtersValues = callbackData;
      }
    })
  }

  goToListingDetials() {
    if (!this.isSelectedMode)
      this.router.navigate(['/ads-and-add-ons/ad-details/' + this.listingDetails.id]);
  }
}
