import { AdsAndAddOnsSheetCycleComponent } from './../../ads-and-add-ons-sheet-cycle/ads-and-add-ons-sheet-cycle.component';
import { AlertHandlerService } from './../../../core/alerts/alert-handler.service';
import { Component } from '@angular/core';
import { StatsBlockComponent } from '../../components/stats-block/stats-block.component';
import { NgFor, NgIf } from '@angular/common';
import { ListingCardRowComponent } from '@src/app/shared/components/listing-card-row/listing-card-row.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';
import { FilterPanelComponent } from '../../components/filter-panel/filter-panel.component';
import { AdAddonsPickerComponent } from '../../components/ad-addons-picker/ad-addons-picker.component';
import { Router } from '@angular/router';
@Component({
  selector: 'app-ads-and-add-ons-page',
  standalone: true,
  imports: [StatsBlockComponent, NgFor, ListingCardRowComponent, NgIf, NtranslatePipe, DynamicHeaderComponent],
  templateUrl: './ads-and-add-ons-page.component.html',
  styleUrl: './ads-and-add-ons-page.component.scss'
})
export class AdsAndAddOnsPageComponent {

  selectedMode: boolean = false;

  dashboardStats = [
    { label: "total_messages", value: 231, icon: 'message-icon' },
    { label: "total_calls", value: 231, icon: 'phone-icon' },
    { label: "total_views", value: 231, icon: 'arrow-top-left' },
    { label: "total_offers", value: 231, icon: 'tag-icon' },
    { label: "total_impressions", value: 231, icon: 'eye-icon' },
  ];

  ads = [
    {
      id: 1,
      title: 'Redmi note 13 pro 4G',
      imageUrl: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      viewCount: 32,
      appearanceCount: 103,
      tags: ['Top Featured'],
    },
    {
      id: 2,
      title: 'شقة للبيع متشطبة أساً',
      imageUrl: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      viewCount: 32,
      appearanceCount: 103,
      tags: ['Top Featured'],
    },
    {
      id: 3,
      title: 'ريس هوندا هورنت ...200',
      imageUrl: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      viewCount: 32,
      appearanceCount: 103,
      tags: ['Ad Bump', 'Top Featured'],
    },
  ];


  filtersValues = {
    sortOption: '',
    callRange: '',
    messageRange: '',
    title: 'اختار تاريخ الاحصائيات',
    selectedDateRange: [],
  }

  selectedAds = new Set<number>();

  constructor(private alertHandlerService: AlertHandlerService) { }

  toggleSelection(adId: number): void {
    if (!this.selectedMode) return
    this.selectedAds.has(adId)
      ? this.selectedAds.delete(adId)
      : this.selectedAds.add(adId);
  }

  isSelected(adId: number): boolean {
    return this.selectedAds.has(adId);
  }

  cancel() {
    this.selectedMode = false;
    this.selectedAds.clear();
  }

  addSelectedAds() {
    console.log('Selected Ads:', Array.from(this.selectedAds));

    if (this.selectedAds.size !== 0)
      this.alertHandlerService.DynamicDialogOpen<AdsAndAddOnsSheetCycleComponent>(AdsAndAddOnsSheetCycleComponent, {}, (callbackData: any) => {
        if (callbackData) {
          this.filtersValues = callbackData;
        }
      })
  }

  openFilter() {
    this.alertHandlerService.DynamicDialogOpen<FilterPanelComponent>(FilterPanelComponent, this.filtersValues, (callbackData: any) => {
      if (callbackData) {
        console.log('Filter applied:', callbackData);
        this.filtersValues = callbackData;
      }
    })

  }

  getActiveFilters(): { key: string; label: string | string[] }[] {
    const filters: { key: string; label: string | string[] }[] = [];

    if (this.filtersValues.sortOption) {
      filters.push({ key: 'sortOption', label: this.filtersValues.sortOption });
    }
    if (this.filtersValues.callRange) {
      filters.push({ key: 'callRange', label: this.filtersValues.callRange });
    }
    if (this.filtersValues.messageRange) {
      filters.push({ key: 'messageRange', label: this.filtersValues.messageRange });
    }

    if (this.filtersValues.selectedDateRange.length > 0) {
      filters.push({ key: 'messageRange', label: this.filtersValues.selectedDateRange });
    }

    return filters;
  }

  removeFilter(key: string, event: MouseEvent) {
    event.stopPropagation(); // Prevent button click triggering other actions
    if (key === 'selectedDateRange') {
      this.filtersValues[key] = [];
    } else {
      this.filtersValues[key] = null;
    }
  }

  clearAllFilters() {
    this.filtersValues = {
      sortOption: null,
      callRange: null,
      title: 'اختار تاريخ الاحصائيات',
      messageRange: null,
      selectedDateRange: []
    };
  }
}
