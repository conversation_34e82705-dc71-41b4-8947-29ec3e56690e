import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, OnD<PERSON>roy, OnInit, computed, inject, signal } from '@angular/core';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { ConversionEvents } from '../../constants/conversion-events';
import { AdsSectionDTO } from '../../models/lookup.model';
import { AdsService } from '../../services/ads.service';
import { CommonService } from '../../services/common.service';
import { CustomMpuComponent } from '../custom-mpu/custom-mpu.component';
import { RegularMpuComponent } from '../regular-mpu/regular-mpu.component';

@Component({
  selector: 'app-mpu-container',
  standalone: true,
  imports: [CommonModule, CustomMpuComponent, RegularMpuComponent],
  templateUrl: './mpu-container.component.html',
  styleUrl: './mpu-container.component.scss'
})
export class MpuContainerComponent implements OnInit, OnDestroy {

  adsContainer = signal<AdsSectionDTO | null>(null);

  hideAds = signal(false);

  browser = inject(BrowserService);

  commonservice = inject(CommonService);

  adsService = inject(AdsService);

  private elementRef: ElementRef = inject(ElementRef);

  private observer!: any;
  options = {
    root: null,
    rootMargin: '0px',
    threshold: 0.3
  };


  @Input()
  set adsContainerInput(value: AdsSectionDTO) {
    this.adsContainer.set(value);
  }

  @Input() handleEvents: boolean = true;
  @Input() keyName!: string;

  isCustomMpu = computed(() => {
    const container = this.adsContainer();
    return container?.locationName?.includes('CustomMpu') ?? false;
  });

  isRegularMpu = computed(() => {
    const container = this.adsContainer();
    return container?.locationName?.includes('RegularMpu') ?? false;
  });

  hasValidAds = computed(() => {
    const container = this.adsContainer();
    return !!(container?.ads && container.ads.length > 0);
  });

  ads = computed(() => {
    return this.adsContainer()?.ads ?? [];
  });

  ngOnInit(): void {
    if (this.handleEvents) {
      this.shouldShowCarouselAd();
    }
  }

  shouldShowCarouselAd(): boolean {
    const ad = this.adsContainer();
    if (!ad || !ad.configurations) return false;

    const configs = ad.configurations.reduce((acc, config) => {
      acc[config.configKey] = config.configValue;
      return acc;
    }, {} as Record<string, string>);


    const minListingViewsCount = parseInt(configs['min_listing_views_count'] || '0', 10);

    if (this.browser.isBrowser()) {
      this.observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.sendEvent();
            this.observer.unobserve(this.elementRef.nativeElement);
          }
        });
      }, this.options);

      this.observer.observe(this.elementRef.nativeElement);

    }

    if (minListingViewsCount > 0) {
      const currentViewCount = this.getListingViewCount();



      if (currentViewCount > minListingViewsCount) {
        this.hideAds.set(true);
        return false;
      }


    }

    return true;
  }

  sendEvent() {
    if (!this.browser.isBrowser()) {
      return;
    }
    this.commonservice.pushDataLayer({
      event: ConversionEvents.ADS_IMPRESSION,
      ads_id: this.adsContainer()?.ads[0]?.id || '',
      ads_sponsore_id: this.adsContainer()?.ads[0]?.sponsorId,
      ads_location: this.adsContainer()?.locationName || '',
      ads_key: this.keyName || ''
    });
  }

  onAdClick() {
    if (!this.browser.isBrowser()) {
      return;
    }
    this.commonservice.pushDataLayer({
      event: ConversionEvents.ADS_CLICK,
      ads_id: this.adsContainer()?.ads[0]?.id || '',
      ads_sponsore_id: this.adsContainer()?.ads[0]?.sponsorId,
      ads_location: this.adsContainer()?.locationName || '',
      ads_key: this.keyName || ''
    });

    this.adsService.adsClick(this.adsContainer()?.ads[0]?.sponsorId).subscribe();
  }

  getListingViewCount(): number {

    if (!this.browser.isBrowser()) {
      return 0;
    }

    const storageKey = `${this.keyName}_listing_views_count`;
    const currentCount = parseInt(sessionStorage.getItem(storageKey) || '0', 10);
    const newCount = currentCount + 1;
    sessionStorage.setItem(storageKey, newCount.toString());
    return newCount;

  }

  ngOnDestroy() {
    if (this.browser.isBrowser()) {
      if (this.observer) {
        this.observer.disconnect();
      }
    }
  }


}
