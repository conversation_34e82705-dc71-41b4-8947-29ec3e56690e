import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { AdsAndAddOnsPageComponent } from './pages/ads-and-add-ons-page/ads-and-add-ons-page.component';
import { AdsAndAddOnsLayoutComponent } from './ads-and-add-ons-layout/ads-and-add-ons-layout.component';
import { AdDetailsComponent } from '../advertisement/components/ad-details/ad-details.component';
import { AdDetialsPageComponent } from './pages/ad-detials-page/ad-detials-page.component';

const routes: Routes = [
  {
    path: '',
    component: AdsAndAddOnsLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'management',
        pathMatch: 'full'
      },
      {
        path: 'management',
        component: AdsAndAddOnsPageComponent,
      },
      {
        path: 'ad-details/:id',
        component: AdDetialsPageComponent,
      },
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes),
    CommonModule,
    NtranslatePipe,
  ],
  exports: [RouterModule],
})
export class AdsAndAddOnsModule { }
