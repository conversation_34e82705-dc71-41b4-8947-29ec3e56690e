import { <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CalendarComponent } from '@src/app/shared/components/calendar/calendar.component';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
import { title } from 'process';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';

@Component({
  selector: 'app-filter-panel',
  standalone: true,
  imports: [
    FormsModule,
    NgFor,
    CalendarComponent,
    SharedBtnComponent,
    DynamicHeaderComponent
  ],
  templateUrl: './filter-panel.component.html',
  styleUrl: './filter-panel.component.scss'
})
export class FilterPanelComponent {

  filtersValues


  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig) { }

  ngOnInit(): void {
    
    this.filtersValues = this.config.data || this.filtersValues;
    console.log(this.config.data);
  }

  close() {
    console.log('Filter values:', this.filtersValues);

    if (this.ref) {
      this.ref.close(this.filtersValues);
    }
  }


}
