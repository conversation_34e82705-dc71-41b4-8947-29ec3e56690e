import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdAddonsWidgetComponent } from '@src/app/shared/components/ad-addons-widget/ad-addons-widget.component';

export interface AddonOption {
  id: string;
  title: string;
  iconId?: string;
  description: string;
  validityValue: string;
  costValue: string;
  subscribed?: boolean;
  daysLeft?: number;
  imageUrl?: string
}

@Component({
  selector: 'app-ad-addons-picker',
  standalone: true,
  imports: [CommonModule, AdAddonsWidgetComponent],
  templateUrl: './ad-addons-picker.component.html',
  styleUrls: ['./ad-addons-picker.component.scss'],
})
export class AdAddonsPickerComponent {

  @Input() applyCount = 3;
  @Input() options: AddonOption[] = [
    {
      id: 'bump',
      title: 'Ad Bump',
      iconId: 'Ad Bump',
      description:
        'تدوير الإعلان وإظهاره بشكل بارز في الصفحة الرئيسية، صفحة الفئة، ونتائج البحث.',
      validityValue: '7',
      costValue: '200',
      subscribed: false,
    },
    {
      id: 'topFeatured',
      title: 'Top Featured Ad',
      iconId: 'Top Featured Ad',
      description:
        'يتم عرض الإعلان مع شارة مميزة بشكل بارز في الصفحة الرئيسية، صفحة الفئة، ونتائج البحث.',
      validityValue: '7',
      costValue: '300',
      subscribed: true,
      daysLeft: 4,
    },
    {
      id: 'highlightAd',
      title: 'Highlight Ad',
      iconId: 'Highlight Ad',
      description:
        'مثال: “سيارة اليوم” — إعلان مميز يتم تثبيته لمدة ٢٤ ساعة ويحتاج إلى موافقة يدوية.',
      validityValue: '24',
      costValue: '2000',
      subscribed: false,
    },
    {
      id: 'pushNotification',
      title: 'Push Notification',
      iconId: 'Push Notification',
      description:
        'إرسال رسائل Push مخصصة لمستخدمين مستهدفين ضمن فئة معينة.',
      validityValue: '',
      costValue: '5000',
      subscribed: false,
    },

  ];

  @Input() disabled = false;

  @Output() close = new EventEmitter<void>();
  @Output() add = new EventEmitter<string>();
  @Output() extend = new EventEmitter<string>();
  @Output() readMore = new EventEmitter<string>();

  onClose() { this.close.emit(); }
  onAdd(id: string) { if (!this.disabled) this.add.emit(id); }
  onExtend(id: string) { if (!this.disabled) this.extend.emit(id); }
  onReadMore(id: string) { this.readMore.emit(id); }

  openDetials() {
    // Logic to open details of the selected add-on
    console.log('Open details for the selected add-on');
  }
}
