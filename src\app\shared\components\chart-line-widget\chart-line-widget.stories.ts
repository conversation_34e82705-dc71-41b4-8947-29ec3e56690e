// src/stories/chart-line-widget.stories.ts
import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { ChartLineWidgetComponent } from './chart-line-widget.component';

const meta: Meta<ChartLineWidgetComponent> = {
    title: 'shared/Chart Line Widget',
    component: ChartLineWidgetComponent,
    decorators: [
        moduleMetadata({
            providers: [],
        }),
    ],
    argTypes: {
        data: {
            control: 'object',
            description: 'Y values for the line',
        },
        title: {
            control: 'text',
            description: 'Card title',
        }
    },
    tags: ['autodocs'],
};
export default meta;

type Story = StoryObj<ChartLineWidgetComponent>;

export const Chart_1: Story = {
    args: {
        title: 'إجمالي الرسائل',
        data: [52, 45, 36, 14, 18, 41, 55],
    },
};

export const Chart_2: Story = {
    args: {
        title: 'الزيارات اليومية',
        data: [12, 28, 20, 10, 34, 30, 48],
    },
};