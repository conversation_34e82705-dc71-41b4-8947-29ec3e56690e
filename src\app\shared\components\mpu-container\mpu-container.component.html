<ng-container *ngIf="hasValidAds() && !hideAds()">
    <div [class.mpu-container-wrapper]="keyName"
         class="mpu-container-wrapper"
         (click)="onAdClick()">
        <ng-container *ngIf="isCustomMpu()">
            <app-custom-mpu *ngFor="let ad of ads()"
                            [ads]="ad">
            </app-custom-mpu>
        </ng-container>

        <ng-container *ngIf="isRegularMpu() || (!isCustomMpu() && !isRegularMpu())">
            <app-regular-mpu *ngFor="let ad of ads()"
                             [ads]="ad">
            </app-regular-mpu>
        </ng-container>
    </div>
</ng-container>