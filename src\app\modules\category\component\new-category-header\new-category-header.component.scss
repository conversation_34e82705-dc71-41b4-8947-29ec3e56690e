@import "mixins.scss";

.category_hero_section {
  margin: 0 16px;
  position: relative;

  // RTL Adjustments
  &.rtl {
    .category-header .slider-cover {
      right: 0;
      left: auto;
    }

    .hero_slider .p-carousel-indicators {
      right: auto !important;
      left: 4% !important;
    }

    .box2 {
      // Mobile RTL clip-path
      clip-path: polygon(100% 0, 0 0, 0% 0%, 40% 100%, 100% 100%);
      
      @media (min-width: 769px) {
        clip-path: polygon(100% 0, 0 0, 0% 0%, 30% 100%, 100% 100%);
      }
    }
  }

  // Gradient Box (Mobile First)
  .box2 {
    width: 200px;
    height: 100%;
    background: linear-gradient(90deg, #f2994a, #bb6bd9);
    clip-path: polygon(0 0, 100% 0, 100% 0%, 66% 100%, 0 100%);
    margin: 0 auto;

    @media (min-width: 769px) {
      width: 390px;
      clip-path: polygon(0 0, 100% 0, 100% 0%, 70% 100%, 0 100%);
    }
  }

  // SVG Filter (Hidden)
  .flt_svg {
    visibility: hidden;
    position: absolute;
    width: 0;
    height: 0;
  }

  .box_parent {
    filter: url("#flt_tag");
    position: relative;
  }

  // Hero Section (Mobile First)
  app-hero-section {
    display: block;
    height: 95%;
    overflow: hidden;
    border-radius: 10px;
    width: 100%;

    @media (min-width: 1001px) {
      display: flex;
      align-items: center;
    }
  }

  // Carousel Navigation (Hidden)
  .carousel_actions {
    .carousel-button-next-item,
    .carousel-button-prev-item {
      display: none !important;
    }
  }

  // Carousel Indicators
  .p-carousel-indicators {
    left: auto;
    right: 4%;

    .dot {
      background-color: #e0e0e0;

      &.active {
        background-color: var(--primary-color);
      }
    }
  }

  // Carousel Items (Mobile First)
  .carousel_items_wrapper {
    padding: 0;
    scroll-padding: 0;
    gap: 0;
    height: 100%;

    @include Large {
      width: 83%;
      margin-inline-start: auto;
    }

    .carousel_item a {
      height: 100%;

      img {
        border-radius: 0;
        height: 100%;
        aspect-ratio: 796 / 378;

        @include Large {
          aspect-ratio: 2620 / 712;
        }
      }
    }
  }

  // Category Header (Mobile First)
  .category-header {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    align-items: center;
    height: 180px;

    @media (min-width: 769px) {
      height: auto;
    }

    .slider-cover {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;

      .slider-cover-background {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        border-bottom-right-radius: 0;
        border-top-right-radius: 5px;
        object-fit: cover;
        z-index: -1;

        &.rtl {
          border-bottom-left-radius: 120px;
          border-top-left-radius: 5px;
          border-bottom-right-radius: 0;
          border-top-right-radius: 0;
        }
      }

      .overlay-content {
        padding: 6px;
        color: white;
        text-align: center;

        @media (min-width: 769px) {
          padding: 20px;
        }

        .title {
          font-size: 18px;
          font-weight: 700;
          line-height: 20px;
          padding-top: 10px;

          @media (min-width: 769px) {
            font-size: 30px;
            line-height: 27px;
          }
        }
      }
    }
  }
}