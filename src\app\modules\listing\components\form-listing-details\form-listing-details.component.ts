import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GeneralSettingsPipe } from '@src/app/shared/pipes/general-settings.pipe';
import { MenuItem } from 'primeng/api/menuitem';
import { ChipsModule } from 'primeng/chips';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SelectButtonModule } from 'primeng/selectbutton';
import { map, Observable, Subscription, tap } from 'rxjs';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { AutoExpandDirective } from 'src/app/modules/listing/directive/autoexpand.directive';
import { itemDetails } from 'src/app/modules/listing/models/item.details.model';
import { InnerListingService } from 'src/app/modules/listing/services/inner-listing.service';
import { ArabicToEnglishNumeralsDirective } from 'src/app/shared/directives/arabictoenglishnum.directive';
import { NoHtmlPasteDirective } from 'src/app/shared/directives/nohtmlpast.directive';
import { LookupDTO, TagDTO } from 'src/app/shared/models/lookup.model';
import { NCurrencyPipe } from 'src/app/shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { LookupService } from 'src/app/shared/services/lookup.service';
import { AdoptionValidatorService } from '../../validator/adoption.validator';
import { HideListingPipe } from '@src/app/modules/advertisement/pipe/hidelisting.pipe';


@Component({
  selector: 'app-form-listing-details',
  standalone: true,
  imports: [CommonModule,
    RadioButtonModule,
    SelectButtonModule,
    FormsModule,
    ReactiveFormsModule,
    InputNumberModule,
    InputTextModule,
    NtranslatePipe,
    AutoExpandDirective,
    NCurrencyPipe,
    ArabicToEnglishNumeralsDirective,
    NoHtmlPasteDirective,
    ChipsModule,
    GeneralSettingsPipe,
    HideListingPipe
  ],
  templateUrl: './form-listing-details.component.html',
  styleUrls: ['./form-listing-details.component.scss']
})
export class FormListingDetailsComponent implements OnInit, OnDestroy {
  @ViewChild('target') targetHost: ElementRef<HTMLInputElement>;
  @Input() editMode: boolean;
  @Input() mainCategory: MenuItem;
  @Input() subCategory: MenuItem;
  @Input() details: itemDetails;
  @Output() onSubmit = new EventEmitter();
  @Output() back = new EventEmitter();
  conditions$: Observable<LookupDTO[]>;
  payments$: Observable<LookupDTO[]>;
  urls: any[] = [];
  coverURL: any = {};
  filesUploaded: any[] = [];
  form: FormGroup;
  maxImages: number = 15;
  maxSize = 5 * 1024 * 1024;
  maxWidth = 1000;
  loadingimage = false;
  watcher: Subscription;
  priceWatcher: Subscription;

  images: { url: string, isCover: boolean }[] = [];
  coverIndex: number;
  predefinedTags$: Observable<TagDTO[]>;

  constructor(
    private lookupService: LookupService,
    public innerService: InnerListingService,
    private browser: BrowserService,
    private fb: FormBuilder,
    private adoptionService: AdoptionValidatorService
  ) { }

  ngOnDestroy(): void {
    this.watcher?.unsubscribe();
    this.priceWatcher?.unsubscribe();
  }

  addTag(tag) {
    const control = this.form.get('keywords') as FormControl;
    const currentKeywords = control.value as TagDTO[];
    const exist = currentKeywords.find(e => e.id == tag.id);
    if (!exist) {
      currentKeywords.push(tag);
      control.setValue(currentKeywords);
    }
  }




  onRemoveTag(item) {
    const control = this.form.get('keywords') as FormControl;
    const currentKeywords = control.value as TagDTO[];
    const updatedKeywords = currentKeywords.filter(
      (keyword) => keyword.id !== item.value.id
    );
    control.setValue(updatedKeywords);
  }

  get hideTags() {
    const control = this.form.get('keywords') as FormControl;
    const currentKeywords = control.value as string[];
    return currentKeywords.length == 0;
  }

  get price() {
    return this.form.get('price') as FormControl;
  }

  ngOnInit(): void {


    this.form = new FormGroup({
      name: new FormControl('', [Validators.required]),
      description: new FormControl('', Validators.required),
      price: new FormControl(null, this.adoptionService.validate()),
      condition: new FormControl('', Validators.required),
      paymentMethod: new FormControl('', Validators.required),
      keywords: new FormControl([]),
    });



    this.predefinedTags$ = this.lookupService.getPredefinedTags(+this.subCategory.id!);
    this.conditions$ = this.lookupService.geCondtions().pipe(map(res => res.data), map(res => {
      return res.map(e => {
        return { id: e.id, name: e.name };
      });
    }), tap(res => {
      if (this.details == null) {
        this.form.patchValue({ condition: res[0] });
      }
    }));


    this.payments$ = this.lookupService.getPaymentMethods().pipe(map(res => res.data), map(res => {
      return res.map(item => {
        return { id: item.id, name: item.name };
      });
    }), tap(res => {
      if (this.details == null) {
        this.form.patchValue({ paymentMethod: res[0].id });
      }
    })
    );

    if (this.details != null && this.details.form) {
      this.form.patchValue(this.details.form);
    }


    this.watcher = this.innerService.fetch.subscribe(res => {
      if (!res) return;
      this.saveData();
    });
  }

  changePayment(e) {
    this.onSubmit.emit({ form: this.form.value });
  }

  removeMainCover() {

  }

  hasError(controlName: string, errorName: string) {
    return this.form.controls[controlName].hasError(errorName);
  }









  saveData() {




    if (!this.form.valid) {



      this.browser.scrollTo({
        behavior: 'smooth',
        top: this.targetHost.nativeElement.offsetTop - 100,
      });
      this.innerService.hasDetailsError = true;
      return;
    }

    this.innerService.hasDetailsError = false;

    this.onSubmit.emit({ form: this.form.value });
  }

  backToCategory() {
    this.back.emit(true);
  }

  generateUid() {
    return new Date().getTime() + '-' + Math.floor(Math.random() * 1000000);
  }

}
