import { CommonModule, DOCUMENT } from '@angular/common';
import { Component, inject, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

export interface ScrollingDotsEffect {
    dotWidth: number;
    dotHeight: number;
    spacing: number;
    maxVisibleDots: number;
    activeDotColor: string;
    dotColor: string;
    animationDuration?: number;
}

@Component({
    selector: 'app-animated-smooth-indicator',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './animated-smooth-indicator.component.html',
    styleUrls: ['./animated-smooth-indicator.component.scss']
})
export class AnimatedSmoothIndicatorComponent implements OnInit, OnChanges {

    isRtl = false;

    @Input() count: number = 0;
    @Input() activeIndex: number = 0;
    @Input() rtl?: boolean; // Optional manual RTL override
    @Input() effect: ScrollingDotsEffect = {
        dotWidth: 8,
        dotHeight: 8,
        spacing: 6,
        maxVisibleDots: 7,
        activeDotColor: '#E6E6E6',
        dotColor: '#A5A7B4',
        animationDuration: 300
    };

    visibleDots: { index: number; isActive: boolean; opacity: number; scale: number }[] = [];
    containerWidth: number = 0;
    translateX: number = 0;

    document = inject(DOCUMENT) as Document;

    ngOnInit(): void {
        // Use manual RTL override if provided, otherwise detect from document
        this.isRtl = this.rtl ?? (this.document.dir === "rtl" || this.document.documentElement.dir === "rtl");

        this.updateVisibleDots();
    }

    ngOnChanges(changes: SimpleChanges): void {
        // Update RTL if manually overridden
        if (changes['rtl']) {
            this.isRtl = this.rtl ?? (this.document.dir === "rtl" || this.document.documentElement.dir === "rtl");
        }

        if (changes['activeIndex'] || changes['count'] || changes['effect'] || changes['rtl']) {
            this.updateVisibleDots();
        }
    }

    private updateVisibleDots(): void {
        if (this.count <= 0) {
            this.visibleDots = [];
            return;
        }

        const maxVisible = this.effect.maxVisibleDots;

        if (this.count <= maxVisible) {
            // Show all dots if count is less than or equal to maxVisible
            this.visibleDots = Array.from({ length: this.count }, (_, index) => ({
                index,
                isActive: index === this.activeIndex,
                opacity: 1,
                scale: 1
            }));
            this.translateX = 0;
        } else {
            // Render ALL dots but control visibility through animation
            this.calculateAllDotsWithVisibility();
        }

        this.calculateContainerWidth();
    }

    private calculateAllDotsWithVisibility(): void {
        const maxVisible = this.effect.maxVisibleDots;
        const halfVisible = Math.floor(maxVisible / 2);

        // Calculate which dots should be in the "visible window"
        let visibleStartIndex: number;
        let visibleCenterPosition: number;

        if (this.activeIndex <= halfVisible) {
            // Near the beginning
            visibleStartIndex = 0;
            visibleCenterPosition = this.activeIndex;
        } else if (this.activeIndex >= this.count - halfVisible - 1) {
            // Near the end
            visibleStartIndex = this.count - maxVisible;
            visibleCenterPosition = this.activeIndex - visibleStartIndex;
        } else {
            // In the middle
            visibleStartIndex = this.activeIndex - halfVisible;
            visibleCenterPosition = halfVisible;
        }

        const visibleEndIndex = visibleStartIndex + maxVisible - 1;

        // Create ALL dots with appropriate visibility and positioning
        this.visibleDots = Array.from({ length: this.count }, (_, index) => {
            const isActive = index === this.activeIndex;
            const isInVisibleRange = index >= visibleStartIndex && index <= visibleEndIndex;

            let opacity = 0.3; // Hidden by default
            let scale = 1;

            if (isInVisibleRange) {
                const positionInVisible = index - visibleStartIndex;
                const distanceFromCenter = Math.abs(positionInVisible - visibleCenterPosition);

                // Full opacity for dots in visible range
                opacity = 1;

                // Fade edges slightly for smoother effect
                if (maxVisible > 3 && (positionInVisible === 0 || positionInVisible === maxVisible - 1)) {
                    opacity = 0.7;
                }

                // No scaling for any dots
                scale = 1;
            }

            return {
                index,
                isActive,
                opacity,
                scale
            };
        });

        // Calculate translateX to center the visible window
        const dotTotalWidth = this.effect.dotWidth + this.effect.spacing;
        let translateX = -visibleStartIndex * dotTotalWidth;

        // Handle RTL mode - reverse the translation direction
        if (this.isRtl) {
            translateX = -translateX;
        }

        this.translateX = translateX;
    }

    private calculateContainerWidth(): void {
        const maxVisible = this.effect.maxVisibleDots;

        if (this.count <= maxVisible) {
            // For few dots, container width is based on actual count
            const visibleCount = this.count;
            this.containerWidth =
                (visibleCount * this.effect.dotWidth) +
                ((visibleCount - 1) * this.effect.spacing);
        } else {
            // For many dots, container width is based on total count (all dots)
            this.containerWidth =
                (this.count * this.effect.dotWidth) +
                ((this.count - 1) * this.effect.spacing);
        }
    }

    getDotStyle(dot: { index: number; isActive: boolean; opacity: number; scale: number }) {
        return {
            'width.px': this.effect.dotWidth,
            'height.px': this.effect.dotHeight,
            'background-color': dot.isActive ? this.effect.activeDotColor : this.effect.dotColor,
            'opacity': dot.opacity,
            'transform': `scale(${dot.scale})`,
            'transition': `all ${this.effect.animationDuration || 300}ms cubic-bezier(0.4, 0.0, 0.2, 1)`,
            'flex-shrink': '0'
        };
    }

    getContainerStyle() {
        return {
            'width.px': this.containerWidth,
            'transform': `translateX(${this.translateX}px)`,
            'transition': `transform ${this.effect.animationDuration || 300}ms cubic-bezier(0.4, 0.0, 0.2, 1)`,
            'display': 'flex',
            'align-items': 'center',
            'gap.px': this.effect.spacing, // CSS gap automatically handles RTL/LTR spacing
            '--max-visible-dots': this.effect.maxVisibleDots,
            '--dot-width': `${this.effect.dotWidth}px`,
            '--dot-spacing': `${this.effect.spacing}px`
        };
    }

    getViewportStyle() {
        const maxVisible = this.effect.maxVisibleDots;
        const viewportWidth = (maxVisible * this.effect.dotWidth) + ((maxVisible - 1) * this.effect.spacing);

        return {
            'width.px': this.count > maxVisible ? viewportWidth : this.containerWidth
        };
    }
}
