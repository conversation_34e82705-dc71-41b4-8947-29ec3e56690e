<ng-container [ngSwitch]="viewSvc.view()">
  <app-business-package *ngSwitchCase="'business'"></app-business-package>

  <app-subscription-package
    *ngSwitchCase="'subscription'"
    [titleText]="subscription.titleText"
    [planTitle]="subscription.planTitle"
    [price]="subscription.price"
  >
  </app-subscription-package>

  <app-package-plan
    *ngSwitchCase="'plan'"
    [badge]="'الأكثر مبيعاً'"
    [planTitle]="plan.title"
    [price]="plan.price"
    [currency]="'جنيه'"
    [billingCycle]="'شهريًا'"
  >
  </app-package-plan>

  <app-custom-subscription
    *ngSwitchCase="'custom'"
    [titleText]="custom.titleText"
  >
  </app-custom-subscription>

  <app-cancel-confirmation
    *ngSwitchCase="'cancel'"
    [titleText]="cancelInfo.titleText"
    [cancelDate]="cancelInfo.cancelDate"
    [btnText]="cancelInfo.btnText"
  >
  </app-cancel-confirmation>

  <div *ngSwitchDefault class="empty">لم يتم اختيار شاشة.</div>
</ng-container>
