import { Component, Input } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { ViewSwitchService } from '../../services/view-switch.service';
@Component({
  selector: 'app-subscription-package',
  standalone: true,
  imports: [SharedBtnComponent, PackageDetailsComponent],
  templateUrl: './subscription-package.component.html',
  styleUrl: './subscription-package.component.scss'
})
export class SubscriptionPackageComponent {
   @Input() titleText: string = '';
   @Input() planTitle: string = '';
   @Input() price: string = '';
   constructor(private view: ViewSwitchService) {}
   goback(){
      this.view.go("business");
    }
}
