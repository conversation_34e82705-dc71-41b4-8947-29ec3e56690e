<div class="dashboard-page">
    <app-dynamic-header [title]="'listing details' | translate" (filterButtonClicked)="openFilter()"
        (backButtonClicked)="backButton()" [showFilterButton]="true">
    </app-dynamic-header>

    <div class="ad-item">
        <img class="ad-thumb" [src]="ads.imageUrl" [alt]="ads.title" />

        <div class="ad-body">
            <h3 class="ad-title">{{ ads.title }}</h3>

            <a class="ad-link" href="#">
                فتح الإعلان
                <app-svg-icons name="back-icon" width="15px" height="15px"></app-svg-icons>
            </a>
        </div>
    </div>


    <app-shared-btn [label]="('add' | translate)" (btnClick)="openAddon()" theme="noraml"></app-shared-btn>

    <div class="tags">
        <div class="tag-label" *ngFor="let tag of ads.tags">
            <app-svg-icons name="featured-icon" width="13px" height="13px" [color]="$any(null)"></app-svg-icons>
            <span>{{ tag }} | ينتهي بعد 2 يوم</span>
        </div>
    </div>

    <app-stats-block [stats]="dashboardStats"></app-stats-block>

    <div *ngFor="let chart of chartData">
        <app-chart-line-widget [data]="chart.data" [title]="chart.name"></app-chart-line-widget>
    </div>
</div>