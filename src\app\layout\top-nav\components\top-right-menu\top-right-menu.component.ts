import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import { BusinessPackageCycleComponent } from '@src/app/modules/business-package/pages/business-package-cycle/business-package-cycle.component';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { getUser } from '@src/app/store/app/selectors/app.selector';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { Observable } from 'rxjs';
import { User } from 'src/app/authentication/models/dto/user.dto.model';
import { TopNotificationIconComponent } from 'src/app/layout/top-nav/components/top-notification-icon/top-notification-icon.component';
import { TopProfileIconComponent } from 'src/app/layout/top-nav/components/top-profile-icon/top-profile-icon.component';
import { SideMenuService } from 'src/app/layout/top-nav/service/side-menu.service';
import { LanguageButtonComponent } from 'src/app/shared/components/language-button/language-button.component';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { ClassNames } from 'storybook/internal/theming';

@Component({
  selector: 'app-top-right-menu',
  standalone: true,
  imports: [CommonModule,
    LanguageButtonComponent,
    RouterModule,
    VerifiedClickDirective,
    RippleModule,
    ButtonModule,
    TopProfileIconComponent,
    TopNotificationIconComponent,
    NtranslatePipe,
  ],

  templateUrl: './top-right-menu.component.html',
  styleUrls: ['./top-right-menu.component.scss'],
  host: { ngSkipHydration: 'true' },


})
export class TopRightMenuComponent implements OnInit, AfterViewInit {

  user$!: Observable<User>;
  logged: boolean;


  isListingDetails$: Observable<boolean>;
  constructor(
    private _authService: AuthService,
    private store: Store,
    private browserService: BrowserService,
    private cdr: ChangeDetectorRef,
    public dv: DeviceDetectionService,
    private menuService: SideMenuService,
    private _router: Router,
    private commonService: CommonService,
    private alertHandlerService: AlertHandlerService
    
  ) {
    this.isListingDetails$ = menuService.isListingDetails$;
  }

  ngAfterViewInit(): void {
    this.cdr.detectChanges();

  }

  ngOnInit(): void {
    if (this.browserService.isBrowser()) {
      this.user$ = this.store.select(getUser);

    }
  }

  UserRole() {
    return this._authService.userValue.role;
  }
  isUserAuth() {
    return true;
    //return this._authService.isUserAuthenticated();
  }

  login() {
    this._authService.redirectLogin();
  }

  goToCreateListing() {
    // this._router.navigate(['/listing/create']);
    this.alertHandlerService.DynamicDialogOpen(
      BusinessPackageCycleComponent,
      {
        className: 'basicModal'
      },
      (callbackData: any) => {
        if (callbackData) {
        }
      }
    );
    this.commonService.pushDataLayer({
      'event': 'add_ad_button_clicked',
      'session_status': 'logged',
    });

    // Track create listing navigation

    // Track create listing navigation
    // this.elasticLogger.logUserAction(
    //   'create_listing_clicked',
    //   'navigation',
    //   this._authService.userValue?.id,
    //   'Create Listing Button',
    //   'Navigation'
    // );

  }

  sendEvent(e) {
    this.commonService.pushDataLayer({
      'event': 'add_ad_button_clicked',
      'session_status': 'unlogged',
    });

    // Track unlogged create listing attempt
    // this.elasticLogger.logUserAction(
    //   'create_listing_attempt_unlogged',
    //   'navigation',
    //   'anonymous',
    //   'Create Listing Button',
    //   'Navigation'
    // );
  }


}
