<div class="date-filter">
    <h3>{{title}}</h3>

    <div class="quick-buttons" *ngIf="showButtons">
        <button type="button" (click)="selectToday()" [ngClass]="{ active: selected === 'today' }">
            {{"today" | translate}}
        </button>
        <button type="button" (click)="selectThisWeek()" [ngClass]="{ active: selected === 'week' }">
            {{"thisWeek" | translate}}
        </button>
        <button type="button" (click)="selectThisMonth()" [ngClass]="{ active: selected === 'month' }">
            {{"thisMonth" | translate}}
        </button>
    </div>

    <p-calendar [(ngModel)]="internalDates" selectionMode="range" dateFormat="dd/mm/yy" inline="true"
        [readonlyInput]="true" (ngModelChange)="onDateSelected($event)"></p-calendar>
</div>