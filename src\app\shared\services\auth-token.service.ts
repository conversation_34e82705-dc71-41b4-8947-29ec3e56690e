import { isPlatform<PERSON>rowser } from '@angular/common';
import { inject, Inject, Injectable, Optional, PLATFORM_ID } from '@angular/core';
import { OAuthService } from 'angular-oauth2-oidc';
import { REQUEST as SSR_REQUEST } from 'ngx-cookie-service-ssr';
import { LogsService } from './consolelogs/logs.service';

@Injectable({
    providedIn: 'root'
})
export class AuthTokenService {
    private tokenCache: string | null = null;
    private lastTokenCheck = 0;
    private readonly CACHE_DURATION = 1000;
    logger = inject(LogsService);

    constructor(
        private oauthService: OAuthService,
        @Inject(PLATFORM_ID) private platformId: Object,
        @Optional() @Inject(SSR_REQUEST) private request: any
    ) { }

    getAccessToken(): string | null {
        const now = Date.now();

        // Use cached token if available and recent
        if (this.tokenCache && (now - this.lastTokenCheck) < this.CACHE_DURATION) {
            return this.tokenCache;
        }

        try {
            if (isPlatformBrowser(this.platformId)) {
                // Browser context
                if (this.oauthService.hasValidAccessToken()) {
                    this.tokenCache = this.oauthService.getAccessToken();
                } else {
                    // Fallback to browser cookie
                    this.tokenCache = this.getBrowserCookieToken();
                }
            } else {
                // Server context
                this.tokenCache = this.getServerSideToken();
            }
        } catch (error) {
            this.logger.error('Error getting access token:', error);
            this.tokenCache = null;
        }

        this.lastTokenCheck = now;
        return this.tokenCache;
    }

    private getServerSideToken(): string | null {
        if (!this.request?.headers?.cookie) {
            return null;
        }

        try {
            const cookies = this.request.headers.cookie;
            const match = cookies.match(/access_token=([^;]+)/);
            return match ? match[1] : null;
        } catch (error) {
            this.logger.error('Error parsing server-side token:', error);
            return null;
        }
    }

    private getBrowserCookieToken(): string | null {
        if (typeof document === 'undefined') {
            return null;
        }

        try {
            const cookies = document.cookie;
            const match = cookies.match(/access_token=([^;]+)/);
            return match ? match[1] : null;
        } catch (error) {
            this.logger.error('Error parsing browser cookie token:', error);
            return null;
        }
    }

    // Clear the token cache when needed
    clearTokenCache(): void {
        this.tokenCache = null;
        this.lastTokenCheck = 0;

        // Also clear the cookie if in browser
        if (isPlatformBrowser(this.platformId)) {
            document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        }
    }
}