@import "mixins.scss";

.hero_section {
  position: relative;
  margin: 0px auto 16px;
  height: 100%;

  @include Large {
    margin: 16px auto;
  }
}

.hero_slider {
  overflow: hidden;
  height: 100%;

  @include Large {
    border-radius: 8px;
  }
}

.p-carousel-indicators {
  position: absolute;
  display: flex;
  top: 86%;
  gap: 5px;
  left: 13%;

  @include Large {
    left: 5%;
  }

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    background-color: #ffffff4d;
    margin: 0;

    &.active {
      background-color: #fff;
    }
  }
}

.carousel_items_wrapper {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scroll-snap-type: x mandatory;
  scrollbar-width: none;
  -ms-overflow-style: none;

  padding: 0 7%;
  scroll-padding: 0 7%;
  gap: 10px;

  @include Large {
    padding: 0;
    scroll-padding: 0;
    gap: 0px;
  }

  .carousel_item {
    scroll-snap-align: start;
    flex-shrink: 0;
    width: 100%;

    a {
      display: block;
      width: 100%;
      line-height: 0;

      img {
        width: 100%;
        border-radius: 8px;
        aspect-ratio: 840 / 490;

        @include Large {
          aspect-ratio: 2880 / 610;
        }
      }
    }
  }

}

.carousel_items_wrapper::-webkit-scrollbar {
  display: none;
}


.carousel_actions {
  display: none;

  .carousel-button-prev-item,
  .carousel-button-next-item {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 42px;
    height: 42px;
    border-radius: 50%;
    display: flex;
    z-index: 3;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.disabled {
      pointer-events: none;
    }

    .pi {
      color: #fff;
    }

  }

  .carousel-button-prev-item {
    left: 0px;
    @include rtl2(left, auto);
    @include rtl2(right, 0px);
    @include rtl2(transform, translateY(-50%) scaleX(-1));
  }


  .carousel-button-next-item {
    right: 0px;
    @include rtl2(right, auto);
    @include rtl2(left, 0px);
    @include rtl2(transform, translateY(-50%) scaleX(-1));

  }

  @include Large {
    display: block;
  }
}