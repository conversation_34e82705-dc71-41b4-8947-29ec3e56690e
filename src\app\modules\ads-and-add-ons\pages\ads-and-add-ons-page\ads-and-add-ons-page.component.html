<div class="dashboard-page">
    <app-dynamic-header [title]="'ad_add_ons_management' | translate" (filterButtonClicked)="openFilter()"
        [showFilterButton]="true"></app-dynamic-header>

    <div *ngIf="getActiveFilters().length > 0" class="quick-buttons">

        <!-- Loop through filters dynamically -->
        <button class="add-button" *ngFor="let filter of getActiveFilters(); let i = index">
            {{ filter.label }}
            <span class="remove-filter" (click)="removeFilter(filter.key, $event)">×</span>
        </button>

        <!-- Date range -->
        <button class="add-button" *ngIf="filtersValues.selectedDateRange?.length > 0">
            {{ 'from' | translate }} {{ filtersValues.selectedDateRange[0].split('/').slice(0, 2).join('/') }}
            <span *ngIf="filtersValues.selectedDateRange[1]">
                -
                {{ 'to' | translate }} {{ filtersValues.selectedDateRange[1].split('/').slice(0, 2).join('/') }}
            </span>
            <span class="remove-filter" (click)="removeFilter('selectedDateRange', $event)">×</span>
        </button>

        <!-- Clear All -->
        <button class="add-button clear-all" (click)="clearAllFilters()">
            {{ 'clearAll' | translate }}
        </button>
    </div>

    <app-stats-block [stats]="dashboardStats"></app-stats-block>

    <div class="section-title">
        <span class="ads-stats">{{'ad_statistics' | translate}}</span>

        <span class="grouped-ads" (click)="selectedMode = !selectedMode" *ngIf="!selectedMode;else selectedModeTemp">
            {{'bulk_ads' | translate}}
        </span>

        <ng-template #selectedModeTemp>
            <div class="action-buttons">
                <button class="add-button" (click)="addSelectedAds()">
                    <!-- <app-svg-icons name="plus" width="16px" height="16px"></app-svg-icons> -->
                    <span>{{'AddItems' | translate}}</span>
                </button>

                <button class="cancel-button" (click)="cancel()">
                    {{'Cancel' | translate}}
                </button>
            </div>
        </ng-template>

    </div>

    <div class="ads-list">
        <div *ngFor="let ad of ads" (click)="toggleSelection(ad.id)">
            <app-listing-card-row [listingDetails]="ad" [isSelectedMode]="selectedMode"
                [isSelected]="isSelected(ad.id)"></app-listing-card-row>
        </div>
    </div>
</div>