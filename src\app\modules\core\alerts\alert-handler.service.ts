import { Injectable, Type } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { BrowserService } from '../service/browser.service';
import { CallWarningComponent } from './components/call-warning/call-warning.component';
import { ErrorMessageComponent } from './components/error-message/error-message.component';
import { GeneralmessageComponent } from './components/generalmessage/generalmessage.component';
import { RatingOfferComponent } from './components/rating-offer/rating-offer.component';
import { RatingReasonsComponent } from './components/rating-reasons/rating-reasons.component';
import { SuccessMessageComponent } from './components/success-message/success-message.component';
import { SuccessOfferSubmittedComponent } from './components/success-offer-submitted/success-offer-submitted.component';
import { SwappLoaderComponent } from './components/swapp-loader/swapp-loader.component';
import { UserRatingsPopupComponent } from './components/user-ratings-popup/user-ratings-popup.component';
import { WarningMessageComponent } from './components/warning-message/warning-message.component';

@Injectable({
  providedIn: 'root'
})
export class AlertHandlerService {
  ref: DynamicDialogRef | undefined;
  loaderRef: DynamicDialogRef | undefined;
  constructor(public dialogService: DialogService, private browser: BrowserService) { }
  message(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(GeneralmessageComponent, {
        data,
        styleClass: 'messageModal',
        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }
    }

  }
  error(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(ErrorMessageComponent, {
        data,
        styleClass: 'alertModal',
        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }
    }
  }

  warn(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(WarningMessageComponent, {
        data,
        styleClass: 'alertModal',

        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }

    }
  }

  successOffer(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(SuccessOfferSubmittedComponent, {
        data,
        styleClass: 'alertModal',

        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }




    }
  }
  success(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(SuccessMessageComponent, {
        data,
        styleClass: 'alertModal',

        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }

    }
  }

  rating(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(RatingOfferComponent, {
        data,
        styleClass: 'alertModal',
        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }
    }
  }

  lowRating(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(RatingReasonsComponent, {
        data,
        styleClass: 'alertModal',
        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }
    }
  }

  DynamicDialogOpen<T>(component: Type<T>, data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(component, {
        data,
        styleClass: 'alertModal ' + data['className'] || '',
        closable: false,
        contentStyle: {
          'max-height': 'calc(100dvh - 5rem)',
          'overflow': 'auto',
          'padding-top': '2rem'
        },
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }

    }
  }

  loader(data: object, callback?, autoclose = true) {
    if (this.browser.isBrowser()) {
      this.loaderRef = this.dialogService.open(SwappLoaderComponent, {
        closable: false,
        closeOnEscape: false,
        data,
        styleClass: 'loaderModal',
        // header: 'Error'
      });

      if (autoclose) {
        // setTimeout(() => {
        //   this.ref!.close();
        // }, 2000);
      }



      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }

    }
  }

  closeLoader() {
    this.loaderRef?.close();

  }

  userRatings(data: object, callback?) {
    if (this.browser.isBrowser()) {
      this.ref = this.dialogService.open(UserRatingsPopupComponent, {
        data,
        styleClass: 'alertModal',
        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }

    }
  }

  call(data: object, callback?) {

    if (this.browser.isBrowser()) {

      this.ref = this.dialogService.open(CallWarningComponent, {
        data,
        styleClass: 'alertModal',

        // header: 'Error'
      });

      if (this.ref) {
        this.ref.onClose.subscribe((res) => {
          if (callback) {
            callback(res);
          }
        });

      }

    }
  }
}
