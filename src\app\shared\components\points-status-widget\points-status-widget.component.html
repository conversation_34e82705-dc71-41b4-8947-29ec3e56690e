<div class="ps-card">
    <div class="ps-header">{{"points_status" | translate}}</div>

    <div class="ps-row">
        <div class="ps-bar-wrap">
            <div class="ps-bar-bg"></div>
            <div class="ps-bar-will-fill" [style.width.%]="percentwithRequired" [class.over]="cannotBuy"
                *ngIf="pointsRequired">
            </div>
            <div class="ps-bar-fill" [style.width.%]="percent" [class.over]="cannotBuy"></div>
        </div>

        <div class="ps-count" [class.over]="cannotBuy">
            <span dir="ltr">
                <span *ngIf="cannotBuy">-</span> {{ pointsRequired ? current + pointsRequired : current }}/{{ total }}
            </span>
        </div>
    </div>

    <div class="ps-details" *ngIf="cannotBuy">
        <div><strong>{{ 'points_you_have' | translate }}:</strong> {{ current }} {{"point" | translate}}</div>
        <div><strong>{{ 'points_consumed' | translate }}:</strong> {{ pointsRequired }} {{"point" | translate}}</div>
        <div><strong>{{ 'points_needed' | translate }}:</strong> {{ pointsRequired }} {{"point" | translate}}</div>
    </div>

    <div class="ps-footer">
        <span class="ps-expiry">{{ expiresAt | date:'longDate' }}</span>
    </div>
</div>