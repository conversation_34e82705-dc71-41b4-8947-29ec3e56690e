import { Title } from '@angular/platform-browser';
import { computed, Injectable, signal } from '@angular/core';

export enum AdsStage {
  Picker = 1,
  Details = 2,
}

@Injectable({
  providedIn: 'root'
})
export class AdsAndAddOnsService {

  Title: string[] = ['اضافة', 'اضافة',]
  private readonly minStage = AdsStage.Picker;
  private readonly maxStage = AdsStage.Details;

  private readonly _stage = signal<number>(AdsStage.Picker);

  stage = this._stage.asReadonly();

  isFirst = computed(() => this._stage() <= this.minStage);
  isLast = computed(() => this._stage() >= this.maxStage);

  nextStage(): void {
    this._stage.update(s => Math.min(s + 1, this.maxStage));
  }

  backStage(): void {
    this._stage.update(s => Math.max(s - 1, this.minStage));
  }

  goTo(stage: AdsStage): void {
    const clamped = Math.max(this.minStage, Math.min(stage, this.maxStage));
    this._stage.set(clamped);
  }

  reset(): void {
    this._stage.set(this.minStage);
  }
}
