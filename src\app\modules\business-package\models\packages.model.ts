export interface PackagePlan {
    id: number;
    name: string;
    currency: string;
    price: number;
    labelTag: string | null;
    renewableLabel: string;
    htmlDescription: string | null;
    packageValidDays: number;
    isCurrentPackage: boolean;
    packageEndDate: string | null;
}

export interface SubscriptionRequest {
    planId: number;
}

export interface SubscriptionResponse {
    success: boolean;
    message: string;
    subscriptionId?: number;
    paymentUrl?: string;
}
