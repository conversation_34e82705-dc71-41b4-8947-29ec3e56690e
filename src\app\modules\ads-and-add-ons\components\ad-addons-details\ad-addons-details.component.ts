import { Component } from '@angular/core';
import { AdAddonsWidgetComponent } from '@src/app/shared/components/ad-addons-widget/ad-addons-widget.component';
import { AddonOption } from '../ad-addons-picker/ad-addons-picker.component';
import { PointsStatusWidgetComponent } from "@src/app/shared/components/points-status-widget/points-status-widget.component";
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { AdsAndAddOnsService } from '../../services/ads-and-add-ons.service';

@Component({
  selector: 'app-ad-addons-details',
  standalone: true,
  imports: [AdAddonsWidgetComponent, PointsStatusWidgetComponent, SharedBtnComponent, NtranslatePipe],
  templateUrl: './ad-addons-details.component.html',
  styleUrl: './ad-addons-details.component.scss'
})
export class AdAddonsDetailsComponent {

  options: AddonOption = {
    id: 'bump',
    title: 'Ad Bump',
    iconId: 'Ad Bump',
    description:
      'تدوير الإعلان وإظهاره بشكل بارز في الصفحة الرئيسية، صفحة الفئة، ونتائج البحث.',
    validityValue: '7',
    costValue: '200',
    subscribed: false,
    imageUrl: '../../../../../../assets/images/Ad Bump.png',
  }

  constructor(private adsAndAddOnsService: AdsAndAddOnsService) { }

  onAdd() {
    this.adsAndAddOnsService.nextStage();
  }
}
