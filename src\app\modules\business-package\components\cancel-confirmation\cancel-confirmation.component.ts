import { Component, Input } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { NgIf } from "@angular/common";
import { ViewSwitchService } from "../../services/view-switch.service";

@Component({
  selector: "app-cancel-confirmation",
  standalone: true,
  imports: [SharedBtnComponent, NgIf],
  templateUrl: "./cancel-confirmation.component.html",
  styleUrl: "./cancel-confirmation.component.scss",
})
export class CancelConfirmationComponent {
  @Input({ required: true }) titleText: string = "";
  @Input() cancelDate: string = "";
  @Input() cancelDateValue: string | null = "";
  @Input() btnText: string = "";
  constructor(private view: ViewSwitchService) {}
  
  goback(){
    this.view.go("business");
  }
}
