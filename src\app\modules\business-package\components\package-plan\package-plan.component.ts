import { Component, inject, Input, ViewEncapsulation } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { CommonModule } from "@angular/common";
import { ViewSwitchService } from "../../services/view-switch.service";
import { PackagesService } from "../../services/packages.service";
import { PackagePlan } from "../../models/packages.model";
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";

@Component({
  selector: "app-package-plan",
  standalone: true,
  imports: [SharedBtnComponent, PackageDetailsComponent, CommonModule, SvgIconsComponent],
  templateUrl: "./package-plan.component.html",
  styleUrl: "./package-plan.component.scss",
  encapsulation: ViewEncapsulation.None,
})
export class PackagePlanComponent {
  plans: PackagePlan[] = [];
  @Input() badge?: string | null = "الأكثر مبيعاً";
  @Input() planTitle?: string = "خطة المبتدئين";
  @Input() price?: string = "3,840";
  @Input() currency = "جنيه";
  @Input() billingCycle = "شهريًا";
  constructor(private view: ViewSwitchService , private packagesService: PackagesService) {}

  

  goto(){
    this.view.go("subscription");
  }
}
