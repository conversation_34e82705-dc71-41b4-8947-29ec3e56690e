import { Component, Input } from '@angular/core';
import { CustomDetailsComponent } from "../custom-details/custom-details.component";
import { InputValueComponent } from "@src/app/shared/components/input-value/input-value.component";
import { NewRangeInputComponent } from "@src/app/shared/components/new-range-input/new-range-input.component";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { ViewSwitchService } from '../../services/view-switch.service';

@Component({
  selector: 'app-custom-subscription',
  standalone: true,
  imports: [CustomDetailsComponent, InputValueComponent, NewRangeInputComponent, SharedBtnComponent],
  templateUrl: './custom-subscription.component.html',
  styleUrl: './custom-subscription.component.scss'
})
export class CustomSubscriptionComponent {
  constructor(private view: ViewSwitchService) {}
  
  @Input() titleText: string = '';

  goback(){
    this.view.go("business");
  }
}
