import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { ListingCardRowComponent } from './listing-card-row.component';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { DialogService } from 'primeng/dynamicdialog';

const meta: Meta<ListingCardRowComponent> = {
    title: 'Shared/ListingCardRow',
    component: ListingCardRowComponent,
    decorators: [
        moduleMetadata({
            providers: [AlertHandlerService, DialogService],
        }),
    ],
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<ListingCardRowComponent>;

export const Default: Story = {
    args: {
        isSelected: false,
        isSelectedMode: false,
        listingDetails: {
            id: 1,
            title: 'Redmi note 13 pro 4G',
            imageUrl: '../../../../../../assets/images/image 4.jpg',
            callCount: 200,
            viewCount: 32,
            appearanceCount: 103,
            tags: ['Top Featured'],
        },
    },
};

export const SelectedMode: Story = {
    args: {
        isSelected: true,
        isSelectedMode: true,

        listingDetails: {
            "id": 1,
            "title": "Redmi note 13 pro 4G",
            "imageUrl": "../../../../../../assets/images/image 4.jpg",
            "callCount": 200,
            "viewCount": 32,
            "appearanceCount": 103,
            "tags": ["Top Featured"]
        }
    }
};
