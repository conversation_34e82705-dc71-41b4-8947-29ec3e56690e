import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { NtranslatePipe } from '../../pipes/ntranslate.pipe';
import { SharedBtnComponent } from '../shared-btn/shared-btn.component';

export interface AddonOption {
  id: string;
  title: string;
  iconId?: string;
  description: string;
  validityValue: string;
  costValue: string;
  subscribed?: boolean;
  daysLeft?: number;
}
@Component({
  selector: 'app-ad-addons-widget',
  standalone: true,
  imports: [NgIf, SvgIconsComponent, NtranslatePipe, SharedBtnComponent, NgClass],
  templateUrl: './ad-addons-widget.component.html',
  styleUrl: './ad-addons-widget.component.scss'
})

export class AdAddonsWidgetComponent {

  @Input() addon?: AddonOption
  @Input() disabled = false;
  @Input() showReadMore = true;
  @Input() reviewMode = false;

  @Output() readMore = new EventEmitter<void>();
  @Output() add = new EventEmitter<void>();

  onReadMore() { this.readMore.emit(); }
  onAdd() { if (!this.disabled) this.add.emit(); }

}