import { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';

import { DynamicHeaderComponent } from './dynamic-header.component';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { DialogService } from 'primeng/dynamicdialog';

const meta: Meta<DynamicHeaderComponent> = {
    title: 'Shared/DynamicHeader',
    component: DynamicHeaderComponent,
    decorators: [
        moduleMetadata({
            imports: [SvgIconsComponent],
            providers: [AlertHandlerService, DialogService]
        }),
    ],
    argTypes: {
        title: { control: 'text' },
        showBackButton: { control: 'boolean' },
        showFilterButton: { control: 'boolean' },
    },
    tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<DynamicHeaderComponent>;

export const WithProjectedFilter: Story = {
    args: {
        title: 'تفاصيل الإعلان',
        showBackButton: true,
        showFilterButton: false,
    },
    render: (args) => (
        {
            props: {
                ...args,
            },
            template: `
        <app-dynamic-header
          [title]="title"
          [showBackButton]="true"
          [showFilterButton]="false"
        >
          <button header-filter class="icon-button" (click)="logFilter()">
            <app-svg-icons name="Top Featured" width="15px" height="15px"></app-svg-icons>
          </button>
        </app-dynamic-header>
        `,

        }
    ),
};

export const AutoFilterButton: Story = {
    args: {
        title: 'تفاصيل الإعلان',
        showBackButton: true,
        showFilterButton: true,
    },
    render: (args) => ({
        props: {
            ...args,
        }
    }),
};

export const AutoCloseButton: Story = {
    args: {
        title: 'تفاصيل الإعلان',
        showBackButton: true,
        showFilterButton: false,
    },
    render: (args) => ({
        props: {
            ...args
        },
    }),
};