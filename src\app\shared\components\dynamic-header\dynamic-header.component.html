<div class="header-summary">
    <div class="header-title">
        <button *ngIf="showBackButton" (click)="onBackButtonClick()" aria-label="Back" class="icon-button">
            <app-svg-icons name="back-icon" width="15px" height="15px"></app-svg-icons>
        </button>
        <span class="page-title">{{ title }}</span>
    </div>

    <!-- Filter slot with default -->
    <ng-container *ngIf="hasFilterSlot; else autoFilter">
        <ng-content select="[header-filter]"></ng-content>
    </ng-container>

    <ng-template #autoFilter>
        <button *ngIf="showFilterButton; else defaultClose" class="icon-button" (click)="onFilterButtonClick()">
            <app-svg-icons name="filter-icon" width="15px" height="15px"></app-svg-icons>
        </button>

        <ng-template #defaultClose>
            <button class="icon-button" (click)="onCloseButtonClick()">
                <app-svg-icons name="closeBtn" width="15px" height="15px"></app-svg-icons>
            </button>
        </ng-template>
    </ng-template>

</div>