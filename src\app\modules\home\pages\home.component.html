<app-categories-tabs class="d-lg-block"></app-categories-tabs>

<div class="home-top-widgets">
  <ng-container *ngIf="banners$ | async as banners">
    <app-hero-section [banners]="banners"></app-hero-section>

  </ng-container>


</div>
<div>
  <div *ngIf="homeSections"
       class="home_sections_box">

    <div *ngFor="let item of homeSections ; let i = index;trackBy : trackby">

      @if(i < (homeSections
        |
        middleSection))
        {
        <app-items-slider
        *ngIf="item.listings && item.listings.length > 0"
        [h2]="true"
        [link]="item"
        [itemsListing]="item.listings | splitList : deviceDetection.isMobile ? 4 : 0"
        [title]="item.label | translate"></app-items-slider>
        }@else if(i == (homeSections | middleSection)){
        <app-smart-banner *isAuthorized="false"></app-smart-banner>
        <app-items-slider *ngIf="item.listings && item.listings.length > 0"
                          [h2]="true"
                          [link]="item"
                          [itemsListing]="item.listings | splitList : deviceDetection.isMobile ? 4 : 0"
                          [title]="item.label | translate"></app-items-slider>
        }@else {

        <div #viewport>
          @defer (on viewport(viewport)) {
          <app-items-slider *ngIf="item.listings && item.listings.length > 0"
                            [h2]="true"
                            [link]="item"
                            [itemsListing]="item.listings | splitList : deviceDetection.isMobile ? 4 : 0"
                            [title]="item.label | translate"></app-items-slider>} @placeholder {
          <div></div>
          }
        </div>
        }

    </div>
  </div>

</div>



<ng-template #loader>
  <app-listing-slider-skeleton></app-listing-slider-skeleton>
</ng-template>