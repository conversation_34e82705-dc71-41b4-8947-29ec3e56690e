@import "mixins";

.layout_btns {
  &::ng-deep {
    .sortDropDown .p-dropdown {
      border: none;
    }
  }
}

@include Large {
  .filter_section {
    .listing_side_filter {
      padding-top: 30px;
    }
  }
}

.listing-ad-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
}

.listing-bottom-ad-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.listing-bottom-ad {
  width: 1100px;
  height: 140px;
  position: relative;
  border-radius: 12px;
  background-color: #6AD737;

  @media (max-width: 992px) {
    width: 100%;
    height: 120px;
  }

  @media (max-width: 576px) {
    height: 100px;
  }
}