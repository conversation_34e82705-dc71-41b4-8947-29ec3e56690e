@import "variables";
@import "mixins";

.dashboard-page {
    direction: inherit;
    padding: 16px;
    background: white;
    min-height: 100vh;
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
}

.ads-stats {
    color: $text-muted-2;
}

.grouped-ads {
    cursor: pointer;
    color: $primary;
    font-weight: bold;
    font-size: 14px;
    padding-bottom: 4px;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.action-buttons,
.quick-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0px;
}

.quick-buttons {
    flex-wrap: wrap;
}

.add-button {
    background-color: $primary-dark;
    color: white;
    border: none;
    border-radius: 16px;
    padding: 6px 14px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.cancel-button {
    background: none;
    border: none;
    color: $primary-dark;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
}

.action-button button {
    background-color: white;
    border: 0.5px solid rgba($primary, 0.2);
    color: $primary-dark;
    border-radius: 6px;
    padding: 5px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    width: 100%;
    text-align: center;
}

.action-button .plus {
    font-weight: bold;
}

.quick-buttons button {
    padding: 0.2rem 1rem;
    border-radius: 6px;
    border: 1px solid #d2a8df;
    background-color: white;
    color: #7a1d8e;
    font-weight: 500;
    cursor: pointer;
    font-family: inherit;
    transition: all 0.2s ease;
}