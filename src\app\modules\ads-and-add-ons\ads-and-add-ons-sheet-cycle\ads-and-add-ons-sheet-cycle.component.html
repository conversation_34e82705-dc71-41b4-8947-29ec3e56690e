<app-dynamic-header title="اضافة" [showBackButton]="!svc.isFirst()" (backButtonClicked)="svc.backStage()" />

<ng-container [ngSwitch]="svc.stage()">
    <ng-container *ngSwitchCase="AdsStage.Picker">
        <div (click)="svc.nextStage()"><app-ad-addons-picker /></div>
    </ng-container>

    <ng-container *ngSwitchCase="AdsStage.Details">
        <div (click)="svc.nextStage()"><app-ad-addons-details /></div>
    </ng-container>

    <ng-container *ngSwitchDefault></ng-container>
</ng-container>