import { CommonModule, isPlatformBrowser } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Inject, Input, OnChanges, OnDestroy, OnInit, PLATFORM_ID, ViewChild, ViewEncapsulation, computed, effect, signal } from '@angular/core';
import { AdsSectionDTO } from '../../models/lookup.model';
import { AnimatedSmoothIndicatorComponent, ScrollingDotsEffect } from '../animated-smooth-indicator/animated-smooth-indicator.component';
import { ListingSlideImageComponent } from '../listing-slide-image/listing-slide-image.component';
import { MpuContainerComponent } from '../mpu-container/mpu-container.component';

export interface CarouselItem {
    type: 'image' | 'ads';
    ads?: AdsSectionDTO;
    data?: {
        imageUrl?: string;
        link?: string;
        [key: string]: any;
    };
}

@Component({
    selector: 'app-listing-carousel',
    standalone: true,
    imports: [CommonModule, ListingSlideImageComponent, M<PERSON><PERSON><PERSON><PERSON><PERSON>omponent, AnimatedSmoothIndicatorComponent],
    templateUrl: './listing-carousel.component.html',
    styleUrls: ['./listing-carousel.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class ListingCarouselComponent implements OnInit, OnDestroy, OnChanges, AfterViewInit {
    // Signal-based inputs
    items = signal<CarouselItem[]>([]);
    showArrows = signal<boolean>(true);
    showIndicators = signal<boolean>(true);
    autoplay = signal<boolean>(false);
    autoplayInterval = signal<number>(5000);
    isRtl = signal<boolean>(false);

    smoothIndicatorEffect: ScrollingDotsEffect = {
        dotWidth: 8,
        dotHeight: 8,
        spacing: 6,
        maxVisibleDots: 7,
        activeDotColor: '#007aff',
        dotColor: '#ffffffa2',
        animationDuration: 300
    };

    // Input setters to update signals
    @Input()
    set itemsInput(value: CarouselItem[]) {
        this.items.set(value);
    }

    @Input()
    set showArrowsInput(value: boolean) {
        this.showArrows.set(value);
    }

    @Input()
    set showIndicatorsInput(value: boolean) {
        this.showIndicators.set(value);
    }

    @Input()
    set autoplayInput(value: boolean) {
        this.autoplay.set(value);
    }

    @Input()
    set autoplayIntervalInput(value: number) {
        this.autoplayInterval.set(value);
    }

    @Input()
    set isRtlInput(value: boolean) {
        this.isRtl.set(value);
    }

    @ViewChild('carouselContainer', { static: false }) carouselContainer!: ElementRef<HTMLDivElement>;
    @ViewChild('carouselTrack', { static: false }) carouselTrack!: ElementRef<HTMLDivElement>;

    // Signal-based state
    currentIndex = signal<number>(0);
    isDragging = signal<boolean>(false);
    startX = signal<number>(0);
    currentX = signal<number>(0);
    translateX = signal<number>(0);
    isInitialized = signal<boolean>(false);
    isBrowser = signal<boolean>(false);

    // Computed signals
    hasMultipleItems = computed(() => this.items().length > 1);
    shouldShowArrows = computed(() => this.showArrows() && this.hasMultipleItems());
    shouldShowIndicators = computed(() => this.showIndicators() && this.hasMultipleItems());
    shouldAutoplay = computed(() => this.autoplay() && this.hasMultipleItems());

    // Non-signal properties
    autoplayTimer: any;
    private startPos: number = 0;
    private threshold: number = 50;

    bigMode = signal(false);

    constructor(@Inject(PLATFORM_ID) private platformId: Object) {
        this.isBrowser.set(isPlatformBrowser(this.platformId));

        // Effect to handle RTL direction changes
        effect(() => {
            if (this.isInitialized()) {
                this.updateCarouselPosition();
            }
        }, { allowSignalWrites: true });

        // Effect to handle autoplay state changes
        effect(() => {
            if (this.shouldAutoplay()) {
                this.startAutoplay();
            } else {
                this.stopAutoplay();
            }
        }, { allowSignalWrites: true });
    }

    ngOnInit(): void {
        // Initialize but don't start autoplay yet
    }


    ngAfterViewInit(): void {
        // Initialize carousel position after view is ready
        setTimeout(() => {
            this.isInitialized.set(true);
            this.updateCarouselPosition();
        });
    }

    ngOnChanges(): void {
        // Reset carousel when items change
        if (this.isInitialized()) {
            this.currentIndex.set(0);
            this.translateX.set(0);
            this.updateCarouselPosition();
        }
    }

    ngOnDestroy(): void {
        this.stopAutoplay();
    }

    // Navigation methods
    goToSlide(index: number): void {
        const items = this.items();
        const currentIdx = this.currentIndex();

        if (index >= 0 && index < items.length && index !== currentIdx) {
            console.log(`Navigating to slide ${index} from ${currentIdx}`);
            this.currentIndex.set(index);
            this.updateCarouselPosition();
            this.resetAutoplay();
        }
    }

    nextSlide(): void {
        const items = this.items();
        if (items.length <= 1) return;

        const currentIdx = this.currentIndex();
        const nextIndex = (currentIdx + 1) % items.length;
        console.log(`Next slide: ${currentIdx} -> ${nextIndex}`);
        this.goToSlide(nextIndex);
    }

    prevSlide(): void {
        const items = this.items();
        if (items.length <= 1) return;

        const currentIdx = this.currentIndex();
        const prevIndex = (currentIdx - 1 + items.length) % items.length;
        console.log(`Previous slide: ${currentIdx} -> ${prevIndex}`);
        this.goToSlide(prevIndex);
    }

    private updateCarouselPosition(): void {
        if (!this.isInitialized() || !this.carouselTrack) {
            console.log('Carousel not initialized or track not available');
            return;
        }

        const currentIdx = this.currentIndex();
        const isRtl = this.isRtl();

        // In RTL, we need to reverse the direction
        const translateValue = isRtl ? currentIdx * 100 : -currentIdx * 100;
        this.translateX.set(translateValue);

        console.log(`Updating carousel position: translateX(${translateValue}%) - RTL: ${isRtl}`);
        this.carouselTrack.nativeElement.style.transition = 'transform 0.3s ease-in-out';
        this.carouselTrack.nativeElement.style.transform = `translateX(${translateValue}%)`;
    }

    // Autoplay methods
    private startAutoplay(): void {
        if (this.isBrowser() && this.shouldAutoplay()) {
            this.autoplayTimer = setInterval(() => {
                this.nextSlide();
            }, this.autoplayInterval());
        }
    }

    private stopAutoplay(): void {
        if (this.autoplayTimer) {
            clearInterval(this.autoplayTimer);
            this.autoplayTimer = null;
        }
    }

    private resetAutoplay(): void {
        if (this.shouldAutoplay()) {
            this.stopAutoplay();
            this.startAutoplay();
        }
    }

    // Touch/Mouse event handlers (only in browser)
    onTouchStart(event: TouchEvent): void {
        const items = this.items();
        if (!this.isBrowser() || items.length <= 1) return;

        this.isDragging.set(true);
        this.startPos = event.touches[0].clientX;
        this.currentX.set(this.startPos);
        this.stopAutoplay();

        // Remove transition during drag
        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'none';
        }
    }

    onTouchMove(event: TouchEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        event.preventDefault();
        const newCurrentX = event.touches[0].clientX;
        this.currentX.set(newCurrentX);
        const diff = this.startPos - newCurrentX;
        const currentIdx = this.currentIndex();
        const isRtl = this.isRtl();

        // Adjust for RTL
        const currentTranslate = isRtl ? currentIdx * 100 : -currentIdx * 100;
        const dragTranslate = (diff / this.carouselContainer.nativeElement.offsetWidth) * 100;
        const finalTranslate = isRtl ? currentTranslate + dragTranslate : currentTranslate - dragTranslate;

        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transform = `translateX(${finalTranslate}%)`;
        }
    }

    onTouchEnd(event: TouchEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        this.isDragging.set(false);
        const diff = this.startPos - this.currentX();
        const isRtl = this.isRtl();

        // Restore transition
        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'transform 0.3s ease-in-out';
        }

        if (Math.abs(diff) > this.threshold) {
            // In RTL, swipe directions are reversed
            if (isRtl) {
                if (diff > 0) {
                    this.prevSlide(); // Swipe right goes to previous in RTL
                } else {
                    this.nextSlide(); // Swipe left goes to next in RTL
                }
            } else {
                if (diff > 0) {
                    this.nextSlide(); // Swipe left goes to next in LTR
                } else {
                    this.prevSlide(); // Swipe right goes to previous in LTR
                }
            }
        } else {
            this.updateCarouselPosition();
        }

        this.resetAutoplay();
    }

    onMouseDown(event: MouseEvent): void {
        const items = this.items();
        if (!this.isBrowser() || items.length <= 1) return;

        this.isDragging.set(true);
        this.startPos = event.clientX;
        this.currentX.set(this.startPos);
        this.stopAutoplay();
        event.preventDefault();

        // Remove transition during drag
        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'none';
        }
    }

    onMouseMove(event: MouseEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        const newCurrentX = event.clientX;
        this.currentX.set(newCurrentX);
        const diff = this.startPos - newCurrentX;
        const currentIdx = this.currentIndex();
        const isRtl = this.isRtl();

        // Adjust for RTL
        const currentTranslate = isRtl ? currentIdx * 100 : -currentIdx * 100;
        const dragTranslate = (diff / this.carouselContainer.nativeElement.offsetWidth) * 100;
        const finalTranslate = isRtl ? currentTranslate + dragTranslate : currentTranslate - dragTranslate;

        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transform = `translateX(${finalTranslate}%)`;
        }
    }

    onMouseUp(event: MouseEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        this.isDragging.set(false);
        const diff = this.startPos - this.currentX();
        const isRtl = this.isRtl();

        // Restore transition
        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'transform 0.3s ease-in-out';
        }

        if (Math.abs(diff) > this.threshold) {
            // In RTL, drag directions are reversed
            if (isRtl) {
                if (diff > 0) {
                    this.prevSlide(); // Drag right goes to previous in RTL
                } else {
                    this.nextSlide(); // Drag left goes to next in RTL
                }
            } else {
                if (diff > 0) {
                    this.nextSlide(); // Drag left goes to next in LTR
                } else {
                    this.prevSlide(); // Drag right goes to previous in LTR
                }
            }
        } else {
            this.updateCarouselPosition();
        }

        this.resetAutoplay();
    }

    onMouseLeave(): void {
        if (this.isDragging()) {
            this.isDragging.set(false);
            // Restore transition
            if (this.carouselTrack) {
                this.carouselTrack.nativeElement.style.transition = 'transform 0.3s ease-in-out';
            }
            this.updateCarouselPosition();
            this.resetAutoplay();
        }
    }

    // Item click handler
    onItemClick(item: CarouselItem, event: Event): void {
        if (Math.abs(this.startPos - this.currentX()) > 10) {
            event.preventDefault();
            return;
        }

        if (item.type === 'ads' && item.data.link) {
            if (this.isBrowser()) {
                window.open(item.data.link, '_blank');
            }
        }
    }

    // Utility methods
    isImageType(item: CarouselItem): boolean {
        return item.type === 'image';
    }

    isAdsType(item: CarouselItem): boolean {
        return item.type === 'ads';
    }

    getImageUrl(item: CarouselItem): string {
        return item.data.imageUrl || '';
    }

    getAdsLink(item: CarouselItem): string {
        return item.data.link || '';
    }


}
