import { CommonModule, DOCUMENT } from '@angular/common';
import { Component, inject, Inject, Input, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { SchemaMarkupService } from '@src/app/core/services/schema-markup.service';
import { SlugPipe } from '@src/app/shared/pipes/slug.pipe';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { CommonService } from '@src/app/shared/services/common.service';
import { LogsService } from '@src/app/shared/services/consolelogs/logs.service';
import { DeviceDetectionService } from '@src/app/shared/services/device-detection.service';
import { environment } from '@src/environments/environment';
import { MenuItem } from 'primeng/api';
import { DialogModule } from 'primeng/dialog';
import { map, tap } from 'rxjs';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { LoveButtonComponent } from 'src/app/shared/components/love-button/love-button.component';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';
import { ListingDetails, ListingStatus } from 'src/app/shared/models/listing.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { FavouriteBaordService } from 'src/app/shared/services/favouritebaord.service';
import { LookupService } from 'src/app/shared/services/lookup.service';
import { getCategories, getLikedItems } from 'src/app/store/app/selectors/app.selector';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-ad-top-links',
  standalone: true,
  imports: [CommonModule, RouterModule, NtranslatePipe, DialogModule, DarkBtnComponent, LoveButtonComponent, VerifiedClickDirective, SlugPipe],
  templateUrl: './ad-top-links.component.html',
  styleUrls: ['./ad-top-links.component.scss'],
  providers: [SlugPipe]
})
export class AdTopLinksComponent implements OnInit {

  @Input() listing: ListingDetails;
  @Input() images: string[];

  likedItems: number[];
  selectedCategory: MenuItem;
  parentCategory: MenuItem;
  shareVisible: boolean = false;
  currentLink: string = "";
  copyText: string = "";
  currentUrl: string = "";
  viewShareBtns!: boolean;

  logger = inject(LogsService);

  constructor(
    private store: Store,
    private favouriteService: FavouriteBaordService,
    private _lookupService: LookupService,
    private translateService: TranslationService,
    private browser: BrowserService,
    @Inject(DOCUMENT) private document: Document,
    private languageService: AppcenterService,
    private slugPipe: SlugPipe,
    public dv: DeviceDetectionService,
    private commonService: CommonService,
    private schema: SchemaMarkupService

  ) {

  }

  ngOnInit(): void {

    if (this.browser.isBrowser()) {
      this.currentLink = location.href;
      this.currentUrl = encodeURIComponent(this.currentLink);

      setTimeout(() => {
        window.scrollTo({
          behavior: 'smooth',
          top: 0,
        });
      }, 0);
    }

    this.viewShareBtns = this.listing.status === ListingStatus.approved;
    this.copyText = this.translateService.instant('Copy Link');

    this.store.select(getCategories).pipe(untilDestroyed(this), map(res => res.menu), map(res => this._lookupService.convertToMenuItems(res)), tap(res => {
      const { category, parent } = this._lookupService.findCategoryById(this.listing.categoryID, res);
      this.selectedCategory = category;
      this.parentCategory = parent;


      const breadcrumbList = [];
      breadcrumbList.push({
        "@type": "ListItem",
        "position": 1,
        "name": this.translateService.instant('Home'),
        "item": environment.clintURL + "/" + this.languageService.lang + "/home"
      });
      if (this.parentCategory) {
        breadcrumbList.push({
          "@type": "ListItem",
          "position": 2,
          "name": this.parentCategory.label,
          "item": environment.clintURL + '/category/' + this.parentCategory.id + '/' + this.slugPipe.transform(this.parentCategory.label)
        });
      }
      if (this.selectedCategory && this.selectedCategory.id !== this.parentCategory.id) {
        breadcrumbList.push({
          "@type": "ListItem",
          "position": 3,
          "name": this.selectedCategory.label,
          "item": environment.clintURL + '/category/' + this.selectedCategory.id + '/' + this.slugPipe.transform(this.selectedCategory.label)
        });
      }


      this.schema.generateProductSchema(this.listing, this.images, breadcrumbList);


    })).subscribe();

    this.store.select(getLikedItems).pipe(untilDestroyed(this)).subscribe(res => this.likedItems = res);
  }

  shareOnMessenger() {

    if (this.browser.isBrowser()) {
      if (this.dv.isMobile) {
        const url = `fb-messenger://share?link=${this.currentUrl}`;
        this.browser.open(url);
      } else {
        const url = `https://www.facebook.com/dialog/send?app_id=341458515256844&link=${this.currentUrl}&display=popup&redirect_uri=${this.currentUrl}`;
        window.open(url, "pop", "width=600, height=400, scrollbars=no");
      }
    }


  }

  shareOnWhatsApp() {
    const url = `https://wa.me/?text=${this.currentUrl}`;
    this.browser.open(url);
  }

  shareOnFacebook() {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${this.currentUrl}`;
    if (this.browser.isBrowser()) {
      if (this.dv.isMobile) {
        this.browser.open(url);
      } else {
        window.open(url, "pop", "width=600, height=400, scrollbars=no");
      }
    }
  }

  shareOnTwitter() {
    const url = `https://twitter.com/intent/tweet?url=${this.currentUrl}`;
    if (this.browser.isBrowser()) {
      window.open(url, "pop", "width=600, height=400, scrollbars=no");
    }
  }
  async shareOnOthers() {

    if (this.browser.isBrowser()) {
      const shareData = {
        title: document.title,
        text: '',
        url: window.location.href,
      };

      try {
        await navigator.share(shareData);
      } catch (err) {
        this.logger.error('Error in sharing: ', err);
      }


    }
  }

  copy() {
    navigator.clipboard.writeText(this.currentLink).then(() => {
      this.copyText = this.translateService.instant('Copied');
    }).catch(err => {
      this.logger.error('Error in copying link: ', err);
    });
  }

  changeFavouriteStatus(id) {
    if (this.isFavourite(id)) {
      this.favouriteService.removeFromFavourite(id);
    } else {
      this.favouriteService.addToFavourite(id);
    }

  }

  isFavourite(id: number) {
    if (this.likedItems) return this.likedItems?.includes(id);
    else return false;

  }

}
