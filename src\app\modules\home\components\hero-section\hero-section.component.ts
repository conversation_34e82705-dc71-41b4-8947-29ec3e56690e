import { CommonModule, NgOptimizedImage } from "@angular/common";
import { ChangeDetectionStrategy, Component, Input, OnInit, signal, ViewChild, ViewEncapsulation, WritableSignal } from "@angular/core";
import { RouterModule } from "@angular/router";
import { BrowserService } from "@src/app/modules/core/service/browser.service";
import { CarouselDirective, CarouselState } from "@src/app/shared/directives/app-carousel.directive";
import { LazyloadDirective } from "@src/app/shared/directives/lazyload.directive";
import { OnviewBannerDirective } from "@src/app/shared/directives/onview-banner.directive";
import { DeviceDetectionService } from "@src/app/shared/services/device-detection.service";
import { HomeBannerSkeletonComponent } from "src/app/shared/components/skeleton/home-banner-skeleton/home-banner-skeleton.component";
import { BannerModel } from "src/app/shared/models/common.model";

@Component({
  selector: "app-hero-section",
  templateUrl: "./hero-section.component.html",
  styleUrls: ["./hero-section.component.scss"],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HomeBannerSkeletonComponent,
    NgOptimizedImage,
    LazyloadDirective,
    CarouselDirective,
    OnviewBannerDirective,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeroSectionComponent implements OnInit {
  @ViewChild("appCarousel") appCarousel!: CarouselDirective;
  @Input() banners: BannerModel[];
  @Input() theme!: string;
  isRtl = signal<boolean>(false);

  imageConfig = {
    width: 430,
    height: 220
  }



  showNavigationArrows: WritableSignal<boolean> = signal(false);
  showIndicators: WritableSignal<boolean> = signal(false);


  state: CarouselState = {
    currentIndex: 0,
    canScrollLeft: false,
    canScrollRight: true,
    totalItems: 0,
    isRtl: false,
  };

  constructor(
    private dv: DeviceDetectionService,
    private browser: BrowserService
  ) {
    if (!this.dv.isMobile) {
      this.showNavigationArrows.set(true);
      this.showIndicators.set(true);

      this.imageConfig.width = 1440;
      this.imageConfig.height = 305;
    }
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    if (this.browser.isBrowser()) {
      this.isRtl.set(document.dir == "rtl");
    }
  }

  onStateChange(newState: CarouselState): void {
    this.state = newState;
  }

  randomFromArray(array: any[]) {
    return array[Math.floor(Math.random() * array.length)];
  }

  trackBy(index, item) {
    return item.id;
  }
}
