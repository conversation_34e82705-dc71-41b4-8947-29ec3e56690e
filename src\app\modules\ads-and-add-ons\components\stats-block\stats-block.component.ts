import { <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, Input } from '@angular/core';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-stats-block',
  standalone: true,
  imports: [NgFor, SvgIconsComponent, NtranslatePipe],
  templateUrl: './stats-block.component.html',
  styleUrl: './stats-block.component.scss'
})
export class StatsBlockComponent {
  @Input() stats: { label: string; value: number; icon: string; }[] = [];
}
