import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { BusinessPackageComponent } from './business-package.component';
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

const meta: Meta<BusinessPackageComponent> = {
  title: 'BusinessPackages/BusinessPackage',
  component: BusinessPackageComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SharedBtnComponent],
      providers: [
        {
          provide: DynamicDialogRef,
          useValue: {
            close: (_?: any) => {},
          },
        },
      ],
    }),
  ],
  parameters: { layout: 'centered' },
  render: (args) => ({ props: args }),
};

export default meta;
type Story = StoryObj<BusinessPackageComponent>;

export const Default: Story = {
  args: {},
};
