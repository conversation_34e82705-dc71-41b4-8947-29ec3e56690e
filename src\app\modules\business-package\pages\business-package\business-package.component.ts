import { Component } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackagePlanComponent } from "../../components/package-plan/package-plan.component";
import { CustomPackagePlanComponent } from "../../components/custom-package-plan/custom-package-plan.component";
import { ViewSwitchService } from "../../services/view-switch.service";
import { DynamicDialogRef } from "primeng/dynamicdialog";
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";
import { PackagesService } from "../../services/packages.service";
import { PackagePlan } from "../../models/packages.model";

@Component({
  selector: "app-business-package",
  standalone: true,
  imports: [
    SharedBtnComponent,
    PackagePlanComponent,
    CustomPackagePlanComponent,
    SvgIconsComponent
],
  templateUrl: "./business-package.component.html",
  styleUrl: "./business-package.component.scss",
})
export class BusinessPackageComponent {
   plans: PackagePlan[] = [];
   
  constructor(private view: ViewSwitchService, private ref: DynamicDialogRef, private packagesService: PackagesService) {}

    ngOnInit() {
    this.packagesService.getPackagePlans().subscribe({
      next: (data) => {
        this.plans = data;
        console.log('plans',this.plans);
      },
      error: (err) => {
        console.error("Error loading packages:", err);
      },
    });
  }
  goToCancel() {
    this.view.go("cancel");
  }
  goToCustom() {
    this.view.go("custom");
  }
  close(){
     this.ref.close({ action: "close" });
  }
}
