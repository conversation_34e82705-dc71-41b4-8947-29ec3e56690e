import { Component } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackagePlanComponent } from "../../components/package-plan/package-plan.component";
import { CustomPackagePlanComponent } from "../../components/custom-package-plan/custom-package-plan.component";
import { ViewSwitchService } from "../../services/view-switch.service";
import { DynamicDialogRef } from "primeng/dynamicdialog";
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";

@Component({
  selector: "app-business-package",
  standalone: true,
  imports: [
    SharedBtnComponent,
    PackagePlanComponent,
    CustomPackagePlanComponent,
    SvgIconsComponent
],
  templateUrl: "./business-package.component.html",
  styleUrl: "./business-package.component.scss",
})
export class BusinessPackageComponent {
  
  constructor(private view: ViewSwitchService, private ref: DynamicDialogRef) {}


  goToCancel() {
    this.view.go("cancel");
  }
  goToCustom() {
    this.view.go("custom");
  }
  close(){
    this.ref.close({ action: "close" });
  }
}
