import { Component, Input } from '@angular/core';
import { LazyloadDirective } from '../../directives/lazyload.directive';
import { AdsDTO } from '../../models/lookup.model';

@Component({
  selector: 'app-regular-mpu',
  standalone: true,
  imports: [LazyloadDirective],
  templateUrl: './regular-mpu.component.html',
  styleUrl: './regular-mpu.component.scss'
})
export class RegularMpuComponent {

  @Input() ads!: AdsDTO;

}
