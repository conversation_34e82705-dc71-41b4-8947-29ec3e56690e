import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { OAuthStorage } from 'angular-oauth2-oidc';

@Injectable({
  providedIn: 'root'
})
export class OAuthCookieStorage implements OAuthStorage {

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  getItem(key: string): string | null {
    if (!isPlatformBrowser(this.platformId)) {
      return null; // Return null during SSR
    }

    try {
      const cookies = document.cookie;
      const match = cookies.match(new RegExp('(^| )' + key + '=([^;]+)'));
      return match ? decodeURIComponent(match[2]) : null;
    } catch (error) {
      console.error('Error reading cookie:', error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    if (!isPlatformBrowser(this.platformId)) {
      return; // Don't set cookies during SSR
    }

    try {
      // Set cookie with 30 days expiry
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 30);
      
      const cookieValue = `${key}=${encodeURIComponent(value)}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;
      document.cookie = cookieValue;
      
      console.log(`OAuthCookieStorage: Set ${key} cookie`);
    } catch (error) {
      console.error('Error setting cookie:', error);
    }
  }

  removeItem(key: string): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      // Set cookie with past expiry date to remove it
      document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`;
      console.log(`OAuthCookieStorage: Removed ${key} cookie`);
    } catch (error) {
      console.error('Error removing cookie:', error);
    }
  }

  clear(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      // Get all OAuth-related cookies and remove them
      const oauthKeys = [
        'access_token',
        'id_token', 
        'refresh_token',
        'token_type',
        'expires_at',
        'scope',
        'state',
        'session_state',
        'nonce',
        'code_verifier',
        'PKCE_verifier'
      ];

      oauthKeys.forEach(key => {
        if (this.getItem(key)) {
          this.removeItem(key);
        }
      });

      console.log('OAuthCookieStorage: Cleared all OAuth cookies');
    } catch (error) {
      console.error('Error clearing cookies:', error);
    }
  }

  /**
   * Check if a specific OAuth token exists
   */
  hasToken(tokenType: 'access_token' | 'id_token' | 'refresh_token'): boolean {
    return !!this.getItem(tokenType);
  }

  /**
   * Get all OAuth tokens as an object
   */
  getAllTokens(): { [key: string]: string | null } {
    return {
      access_token: this.getItem('access_token'),
      id_token: this.getItem('id_token'),
      refresh_token: this.getItem('refresh_token'),
      token_type: this.getItem('token_type'),
      expires_at: this.getItem('expires_at'),
      scope: this.getItem('scope')
    };
  }

  /**
   * Debug method to log all OAuth cookies
   */
  debugLogCookies(): void {
    if (!isPlatformBrowser(this.platformId)) {
      console.log('OAuthCookieStorage: Not in browser, cannot access cookies');
      return;
    }

    const tokens = this.getAllTokens();
    console.log('OAuthCookieStorage: Current OAuth cookies:', tokens);
  }
}