import { Meta, moduleMetadata, StoryObj } from "@storybook/angular";
import { SharedBtnComponent } from "./shared-btn.component";

import "../../../../styles/style-files/_variables.scss";

const meta: Meta<SharedBtnComponent> = {
  title: "Shared/SharedBtn",
  component: SharedBtnComponent,
  tags: ["autodocs"],
  render: (args) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<SharedBtnComponent>;

export const Default: Story = {
  args: {
    label: "التالي",
    size: "large",
  },
};

export const WithCustomLabel: Story = {
  args: {
    label: "اضغط هنا",
    size: "large",
  },
};

export const custom: Story = {
  args: {
    label: " الغاء تجديد الباقة",
    size: "large",
    bgcolor: "#B1362F",
    boxShadow: "#B1362F",
  },
};

export const nobtncolor: Story = {
  args: {
    label: " لا تلغى",
    size: "large",
    bgcolor: "white",
    labelColor: "#722282",
    boxShadow: "none",
  },
};
export const btnwithicon: Story = {
  args: {
    label: " شراء نقاط ",
    size: "small",
    iconName: "icon-circle-plus",
    iconWidth: "10px",
    iconHeight: "10px",
    theme: "bgWhite"
  },
};

export const typeBgWhite: Story = {
  args: {
    label: " لا تلغى",
    size: "large",
    theme: "bgWhite"
  },
};

export const TypeError: Story = {
  args: {
    label: " لا تلغى",
    size: "large",
    theme: "error"
  },
};

export const Disabled: Story = {
  args: {
    label: "جاري المعالجة",
    size: "large",
    disabled: false,
  },
};