import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PointsStatusWidgetComponent } from './points-status-widget.component';

describe('PointsStatusWidgetComponent', () => {
  let component: PointsStatusWidgetComponent;
  let fixture: ComponentFixture<PointsStatusWidgetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PointsStatusWidgetComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(PointsStatusWidgetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
