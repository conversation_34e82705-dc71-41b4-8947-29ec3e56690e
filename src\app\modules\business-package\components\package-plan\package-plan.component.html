<div class="plan-container">
  <div class="plan-card" *ngFor="let plan of plans">
    <div class="badge-wrapper"*ngIf="plan.labelTag">
      <div class="badge"> {{ plan.labelTag }} </div>
    </div>
    <div class="plan-content">
      <div class="price-section">
        <div class="price">
          <span class="price-value">{{ plan.price }}</span>
          <span class="price-unit">جنيه/شهريًا</span>
        </div>
        <div class="plan-title">{{ plan.name }}</div>
      </div>
      <div [innerHTML]="plan.htmlDescription" *ngIf="plan.htmlDescription"></div>
           <div class="renewal-note" *ngIf="plan.renewableLabel ">
            <span class="note-text">{{ plan.renewableLabel }} {{plan.packageValidDays}} Days</span>
            <span class="note-icon">
              <app-svg-icons name="info-icon" width="12px" height="12px"></app-svg-icons>
            </span>
        </div>
    </div>
    
      <app-shared-btn
          [label]="'اشترك'"
          [size]="'large'"
          [bgcolor]="'#722282'"
          [labelColor]="'#fff'"
          (click)="goto()"
      ></app-shared-btn>
  </div>
</div>