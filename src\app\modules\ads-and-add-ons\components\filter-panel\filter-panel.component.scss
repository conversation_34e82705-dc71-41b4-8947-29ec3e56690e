@import "variables";

.filter-panel {
    background: #fff; // you could also define a $white var if used often
    width: 100%;
    max-width: 400px;

    .section-title {
        font-size: 16px;
        font-weight: 700;
        margin-top: 20px;
        margin-bottom: 10px;
        color: $text-color;
        font-family: $f-b;
    }

    .filter-label {
        font-weight: 600;
        margin-bottom: 8px;
        margin-top: 16px;
        color: $text-color;
        font-family: $f-r;
    }

    .p-button {
        font-size: 13px;
        padding: 6px 10px;
        font-family: $f-r;
    }

    .p-calendar {
        margin-bottom: 20px;
    }
}

.quick-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    justify-content: flex-start;
    flex-wrap: wrap;
}

button {
    padding: 0.2rem 1rem;
    border-radius: 6px;
    border: 1px solid lighten($primary, 25%); // replaces #d2a8df
    background-color: #fff;
    color: $primary; // replaces #7a1d8e
    font-weight: 500;
    cursor: pointer;
    font-family: $f-r;
    transition: all 0.2s ease;

    &.active {
        background-color: lighten($primary, 60%); // replaces #e9d7f1
        border: 1px solid transparent;
        font-weight: 700;
        color: $primary;
    }
}