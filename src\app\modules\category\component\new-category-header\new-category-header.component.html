<div *ngIf="banners$ | async as banners"
     class="category_hero_section"
     [class.rtl]="isRtl">
  @if(banners && isOriginal()){
  <app-category-header [category]="category"
                       [subCategoryId]="subCategoryId"></app-category-header>

  } @else {
  <div class="container">
    <div class="category-header">
      <app-hero-section [banners]="banners"></app-hero-section>

      <ng-container *ngIf="stickerImage$ | async as stickerImage">
        <div class="box_parent slider-cover">
          <div class="box2"
               [ngClass]="{ rtl: isRtl }"
               *ngIf="stickerImage">
            <img [src]="(stickerImage$ | async)?.url"
                 [alt]="categoryInfo.name"
                 class="slider-cover-background" />
            <div class="overlay-content">
              <h3 class="title">
                {{ categoryInfo.name | translate }}
              </h3>
            </div>
          </div>

          <svg class="flt_svg"
               xmlns="http://www.w3.org/2000/svg">
            <defs>
              <filter id="flt_tag">
                <feGaussianBlur in="SourceGraphic"
                                stdDeviation="8"
                                result="blur" />
                <feColorMatrix in="blur"
                               mode="matrix"
                               values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -9"
                               result="flt_tag" />
                <feComposite in="SourceGraphic"
                             in2="flt_tag"
                             operator="atop" />
              </filter>
            </defs>
          </svg>
        </div>
      </ng-container>
    </div>
  </div>

  }
</div>