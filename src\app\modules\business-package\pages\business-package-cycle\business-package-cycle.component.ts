import { CommonModule } from "@angular/common";
import { Component, inject, signal, Signal } from "@angular/core";
import { BusinessPackageComponent } from "../business-package/business-package.component";
import { SubscriptionPackageComponent } from "../../components/subscription-package/subscription-package.component";
import { PackagePlanComponent } from "../../components/package-plan/package-plan.component";
import { CancelConfirmationComponent } from "../../components/cancel-confirmation/cancel-confirmation.component";
import { ViewSwitchService } from "../../services/view-switch.service";
import { CustomSubscriptionComponent } from "../../components/custom-subscription/custom-subscription.component";
import { AlertHandlerService } from "@src/app/modules/core/alerts/alert-handler.service";
import { PackagesService } from "../../services/packages.service";
import { PackagePlan } from "../../models/packages.model";

@Component({
  selector: "app-business-package-cycle",
  standalone: true,
  imports: [
    CommonModule,
    BusinessPackageComponent,
    SubscriptionPackageComponent,
    PackagePlanComponent,
    CancelConfirmationComponent,
    CustomSubscriptionComponent,
  ],
  templateUrl: "./business-package-cycle.component.html",
  styleUrl: "./business-package-cycle.component.scss",
})
export class BusinessPackageCycleComponent {
  plans: PackagePlan[] = [];
  
  public viewSvc = inject(ViewSwitchService);
  private alerts = inject(AlertHandlerService);

  constructor( private packagesService: PackagesService) {}
      ngOnInit() {
    this.packagesService.getPackagePlans().subscribe({
      next: (data) => {
        this.plans = data;
        console.log('plans',this.plans);
      },
      error: (err) => {
        console.error("Error loading packages:", err);
      },
    });
  }
  plan = {
    title: "خطة المبتدئين (Starter Plan)",
    price: "3,840",
  };

  proPlan = {
    title: "برو للأعمال (Business Pro)",
    price: "7,000",
  };

  custom = {
    titleText: "المؤسسات (Enterprise)",
  };

  subscription = {
    titleText: " اشترك في باقة خطة المبتدئين",
    planTitle: "خطة المبتدئين",
    price: "3,840",
  };

  cancelInfo = {
    titleText: "هل أنت متأكد من رغبتك في تحويل الي حساب فرد؟  ",
    cancelDate:
      "عند تحويل نوع الحساب ، سيتم إزالة أي إضافة او اعلانات تم شراؤها مسبقًا",
    btnText: "تأكيد التحويل ",
  };
  openBySwitch(view?: string) {
    this.alerts.DynamicDialogOpen(
      BusinessPackageCycleComponent,
      {},
      (res) => {}
    );

  }
}
