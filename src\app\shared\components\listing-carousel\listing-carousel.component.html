<div class="listing-carousel"
     [attr.dir]="isRtl() ? 'rtl' : 'ltr'"
     [class.bigmode]="bigMode()"
     *ngIf="items().length > 0">

    <span class="carousel_close"
          (click)="bigMode.set(false)">
        <i class="pi pi-times"></i>
    </span>


    <!-- Carousel Container -->
    <div class="carousel-container"
         #carouselContainer
         (touchstart)="onTouchStart($event)"
         (touchmove)="onTouchMove($event)"
         (touchend)="onTouchEnd($event)"
         (mousedown)="onMouseDown($event)"
         (mousemove)="onMouseMove($event)"
         (mouseup)="onMouseUp($event)"
         (mouseleave)="onMouseLeave()">

        <!-- Carousel Track -->
        <div class="carousel-track"
             #carouselTrack
             [style.transform]="'translateX(' + translateX() + '%)'">

            @for (item of items(); track $index) {
            <div class="carousel-slide"
                 [class.ads]="isAdsType(item)"
                 [class.active]="$index === currentIndex()"
                 (click)="bigMode.set(true);onItemClick(item, $event)">

                <!-- Image Type -->
                @if (isImageType(item)) {
                <app-listing-slide-image
                                         [image]="{url : item.data.imageUrl , alt : item.data.imageUrl}"></app-listing-slide-image>
                }

                @if (isAdsType(item)) {
                <app-mpu-container [adsContainerInput]="item.ads"
                                   [keyName]="'carousel_ads_' + item.ads[0]?.id"></app-mpu-container>
                }
            </div>
            }
        </div>

        <!-- Navigation Arrows -->
        @if (shouldShowArrows()) {
        <button class="carousel-arrow carousel-arrow-prev"
                (click)="prevSlide()"
                type="button"
                aria-label="Previous slide">
            <i class="pi pi-chevron-left"></i>
        </button>

        <button class="carousel-arrow carousel-arrow-next"
                (click)="nextSlide()"
                type="button"
                aria-label="Next slide">
            <i class="pi pi-chevron-right"></i>
        </button>
        }

        <!-- Slide Counter -->
        <div class="carousel-counter">
            <i class="pi pi-camera"></i>
            <span>{{ currentIndex() + 1 }} / {{ items().length }}</span>
        </div>
    </div>

    <!-- Indicators -->
    @if (shouldShowIndicators()) {

    @if (items().length > 1) {
    <div class="carousel-smooth-indicators">
        <app-animated-smooth-indicator [count]="items().length"
                                       [activeIndex]="currentIndex()"
                                       [effect]="smoothIndicatorEffect">
        </app-animated-smooth-indicator>
    </div>
    }
    }
</div>