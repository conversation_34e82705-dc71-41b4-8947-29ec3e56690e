<form [formGroup]="form" autocomplete="off" #target>

    <div *ngIf="mainCategory" class="row form_area">

        <div class="input_box">
            <div class="inputField" [ngClass]="{'required_input' : hasError('name' , 'required')}">
                <input type="text" pInputText
                    [maxlength]="'listing-title-max-character' | generalSettings : 160 | async"
                    [placeholder]="'Item Name' | translate" formControlName="name" />
                <b *ngIf="hasError('name' , 'required')" class="required">*</b>
            </div>

            <div class="help_msg">
                <i class="pi pi-info-circle"></i>{{ "create_listing_help_description" | translate }}
            </div>
            <div class="inputField textareaField" [ngClass]="{'required_input' : hasError('description' , 'required')}">
                <textarea [placeholder]="'Item Name Description' | translate" appNoHtmlPaste appAutoExpand rows="4"
                    formControlName="description"></textarea>
                <b *ngIf="hasError('description' , 'required')" class="required">*</b>
            </div>
        </div>
        <ng-container *ngIf="predefinedTags$ | async as predefinedTags">
            <div class="tags_box" *ngIf="predefinedTags.length > 0">
                <h2>{{ 'Add Tags' | translate }} <span>{{ 'Optional' | translate}}</span></h2>
                <ul class="predefined_tags">
                    <li *ngFor="let tag of predefinedTags" (click)="addTag(tag)">{{ tag.name | translate }}</li>
                </ul>
                <div class="tags_input" [hidden]="hideTags">
                    <p-chips formControlName="keywords" (onRemove)="onRemoveTag($event)"
                        [placeholder]="'Choose_tags' | translate">
                        <ng-template let-item pTemplate="item">
                            {{item.name | translate}}
                        </ng-template>
                    </p-chips>
                </div>
            </div>
        </ng-container>



        <div class="toggle_list condition_box" *ngIf="conditions$ | async as conditions"
            [hidden]="subCategory | hidelisting:'hideListingCondition'">
            <h3>{{ 'Condition' | translate }} <b *ngIf="hasError('condition' , 'required')" class="required">*</b></h3>
            <p-selectButton [ngClass]="{'required_input' : hasError('condition' , 'required')}" [options]="conditions"
                formControlName="condition" optionLabel="label">
                <ng-template let-item pTemplate="item">
                    <div class="select_element">{{ item.name | translate }}</div>
                </ng-template>
            </p-selectButton>
        </div>

        <div class="help_msg">
            <i class="pi pi-info-circle"></i>{{ "create_listing_help_price" | translate }}
        </div>
        <div class="price">
            <div class="p-inputgroup" [ngClass]="{'required_input' : hasError('price' , 'required')}">

                <p-inputNumber formControlName="price" appArabicToEnglishNumerals [maxlength]="12" mode="decimal"
                    [minFractionDigits]="0" [maxFractionDigits]="2" [placeholder]="'Price' | translate" />

                <span class="p-inputgroup-addon" [innerHTML]="'' | ncurrency"></span>
                <b *ngIf="hasError('price' , 'required')" class="required">*</b>

            </div>
        </div>

        <div class="payments" *ngIf="payments$ | async as payments"
            [hidden]="subCategory | hidelisting:'hideListingCondition'">
            <h3>{{ 'PaymentType' | translate }}<b *ngIf="hasError('paymentMethod' , 'required')" class="required">*</b>
            </h3>
            <ng-container *ngFor="let payment of payments" class="field-checkbox">
                <div class="payment_item">
                    <p-radioButton [inputId]="payment.id.toString()" [value]="payment.id"
                        formControlName="paymentMethod" (onClick)="changePayment($event)"></p-radioButton>
                    <label [for]="payment.id" class="ml-2">{{ payment.name | translate }}</label>
                </div>
            </ng-container>
        </div>





    </div>