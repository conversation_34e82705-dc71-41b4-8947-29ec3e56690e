.listing-carousel {
    position: relative;
    width: 100%;
    max-width: 816px;
    margin: 0 auto;
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
    aspect-ratio: 816 / 459;

    &.no-items {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border: 2px dashed #dee2e6;

        .no-items-content {
            text-align: center;
            color: #6c757d;

            i {
                font-size: 3rem;
                margin-bottom: 1rem;
                opacity: 0.5;
            }

            p {
                margin: 0;
                font-size: 1.1rem;
            }
        }
    }

    &.bigmode {
        position: fixed;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        z-index: 100;
        display: block;
        justify-content: center;
        align-items: center;
        background: #a5a7b4;
        max-width: initial;

        .carousel_close {
            display: flex;
        }

        .carousel-counter {
            top: 3rem;
        }


    }
}

.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: grab;

    &:active {
        cursor: grabbing;
    }
}

.carousel-track {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
}

.carousel-slide {
    flex: 0 0 100%;
    width: 100%;
    height: 100%;
    position: relative;

    &.ads {
        background: #a5a7b4;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

// Navigation Arrows
.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
    text-shadow: 0px 0px 10px #0000008f;

    i {
        font-size: 1rem;
        color: #fff;
    }


    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    &.carousel-arrow-prev {
        left: 1rem;
    }

    &.carousel-arrow-next {
        right: 1rem;
    }
}

// Slide Counter
.carousel-counter {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(45, 49, 66, 0.8);
    backdrop-filter: blur(10px);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 2;

    i {
        font-size: 0.75rem;
        opacity: 0.8;
    }
}

.carousel_close {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    background: rgb(118 121 136);
    width: 24px;
    height: 24px;
    display: none;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 50%;
    color: #fff;
}



// Responsive Design
@media (max-width: 768px) {
    .carousel-arrow {
        width: 36px;
        height: 36px;

        i {
            font-size: 0.875rem;
        }

        &.carousel-arrow-prev {
            left: 0.5rem;
        }

        &.carousel-arrow-next {
            right: 0.5rem;
        }
    }

    .carousel-counter {
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.375rem 0.625rem;
        font-size: 0.8rem;
    }


}


// Animation keyframes
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.95);
    }

    100% {
        opacity: 0.8;
        transform: scale(1);
    }
}

// Touch/drag improvements
.carousel-container {
    touch-action: pan-y;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

// High contrast mode support
@media (prefers-contrast: high) {
    .carousel-arrow {
        background: white;
        border: 2px solid black;

        i {
            color: black;
        }
    }

    .carousel-counter {
        background: black;
        color: white;
        border: 1px solid white;
    }


}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    .carousel-track {
        transition: none;
    }

    .carousel-arrow {
        transition: none;
    }

}

.listing-carousel[dir="rtl"] {

    .carousel-arrow-prev {
        right: 10px;
        left: auto;

        .pi-chevron-left::before {
            content: "\e900";
        }
    }

    .carousel-arrow-next {
        left: 10px;
        right: auto;

        .pi-chevron-right::before {
            content: "\e901";
        }
    }

    .carousel-counter {
        left: 10px;
        right: auto;
    }

}