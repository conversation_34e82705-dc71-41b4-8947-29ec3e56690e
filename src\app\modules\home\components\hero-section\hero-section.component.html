<div class="hero_section container">
  <div [class]="'hero_slider ' + theme "
       *ngIf="banners; else loading">
    <div appCarousel
         [totalItems]="banners?.length"
         [rtl]="isRtl()"
         [autoplay]="true"
         [autoplayTime]="15000"
         (stateChange)="onStateChange($event)"
         #appCarousel="appCarousel"
         class="carousel_items_wrapper">
      <div class="carousel_item"
           *ngFor="let bannerItem of banners; trackBy: trackBy"
           [onviewbanner]="bannerItem">
        @if(bannerItem.index == 0){
        <a [attr.href]="bannerItem.isAd ? (bannerItem.adClickRouteUrl || bannerItem.bannerURL) : bannerItem.bannerURL"
           [target]="bannerItem.isAd ? '_blank' : '_self'"
           rel="nofollow">
          <img [ngSrc]="bannerItem.imageURL"
               [alt]="bannerItem.imageURL + '?v=1.1.2'"
               [width]="imageConfig.width"
               [height]="imageConfig.height"
               priority />
        </a>
        }@else {
        <a [attr.href]="bannerItem.isAd ? (bannerItem.adClickRouteUrl || bannerItem.bannerURL) : bannerItem.bannerURL"
           [target]="bannerItem.isAd ? '_blank' : '_self'"
           rel="nofollow">
          <img [lazyload]="bannerItem.imageURL"
               [alt]="bannerItem.imageURL + '?v=1.1.2'"
               [width]="imageConfig.width"
               [height]="imageConfig.height" />
        </a>
        }
      </div>
    </div>
    <div class="carousel_actions"
         *ngIf="banners.length >= 2">
      <div class="carousel-button-prev-item"
           (click)="appCarousel.scrollTo('left')">
        <i class="pi pi-chevron-left"></i>
      </div>
      <div class="carousel-button-next-item"
           (click)="appCarousel.scrollTo('right')">
        <i class="pi pi-chevron-right"></i>
      </div>


    </div>
    <div class="p-carousel-indicators"
         #dotsContainer
         *ngIf="banners.length >= 2">
      <div *ngFor="let dot of banners; let i = index"
           class="dot"
           [class.active]="appCarousel.currentIndex === i"
           (click)="appCarousel.gotoSlide(i)"></div>
    </div>
  </div>

  <ng-template #loading>
    <app-home-banner-skeleton></app-home-banner-skeleton>
  </ng-template>
</div>