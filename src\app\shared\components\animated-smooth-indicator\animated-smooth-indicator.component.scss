.smooth-indicator-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-top: -20px;
}

.smooth-wrapper {
    overflow: hidden;
}

.smooth-indicator-viewport {
    display: flex;
    justify-content: start;
    align-items: start;
    background: #2d31428a;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 6px 13px;

}



.smooth-indicator-track {
    display: flex;
    align-items: center;
    transition: transform 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.smooth-indicator-dot {
    border-radius: 50%;
    flex-shrink: 0;
    transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);

    &:last-child {
        margin-right: 0 !important;
        margin-left: 0 !important;
    }
}

// RTL support
.smooth-indicator-track[dir="rtl"] {
    flex-direction: row-reverse;
    
    .smooth-indicator-dot {
        &:first-child {
            margin-right: 0 !important;
        }
        
        &:last-child {
            margin-left: 0 !important;
        }
    }
}

// Custom styles for carousel integration
.carousel-smooth-indicators {
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(45, 49, 66, 0.54);
    backdrop-filter: blur(25px);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 6px 13px;
    margin: 0px;
    z-index: 3;

    .smooth-indicator-container {
        padding: 0;
    }
}