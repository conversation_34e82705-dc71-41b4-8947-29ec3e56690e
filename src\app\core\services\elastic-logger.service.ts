import { HttpClient, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { AuthService } from '@src/app/shared/services/auth.service';
import { LogsService } from '@src/app/shared/services/consolelogs/logs.service';
import { DeviceDetectionService } from '@src/app/shared/services/device-detection.service';
import { SettingsFacade } from '@src/app/shared/services/setting-facad.service';
import { Observable, of, Subject, timer } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';
import { ConversionEvents } from 'src/app/shared/constants/conversion-events';
import { environment } from 'src/environments/environment';
import { BrowserService } from './../../modules/core/service/browser.service';

export interface ListingViewData {
  listingId: string;
  userId?: string;
  categoryId?: string;
  listingName?: string;
  price?: number;
  categoryName?: string;
  viewMode?: string;
  userAgent?: string;
  referrer?: string;
  url?: string;
}

export interface UserInteractionData {
  listings_id?: string;
  user_id?: string;
  seller_id?: string;
  transction_date?: string;
  listing_name?: string;
  category_name?: string;
  user_action?: string;
  request_source?: 'web_desktop' | 'web_mobile';
  platform?: 'web';

}

@Injectable({ providedIn: 'root' })
export class ElasticLoggerService {
  logger = inject(LogsService);
  private userInteractionUrl = environment.userInteractionAPI;
  private eventBatch: UserInteractionData[] = [];
  private batchTimer: any;
  private BATCH_SIZE = 5;
  private BATCH_TIMEOUT = 30000; // 30 seconds
  private destroy$ = new Subject<void>();
  private isBrowser: boolean;
  private targetEvens: string[] = [
    ConversionEvents.Listing_View,
    ConversionEvents.Add_Comment,
    ConversionEvents.Make_Offer,
    ConversionEvents.Post_Listing_Success,
    ConversionEvents.Contact_View,
    ConversionEvents.Contact_Inquiry,
  ];

  constructor(
    private http: HttpClient,
    private browserService: BrowserService,
    private dvService: DeviceDetectionService,
    private settingService: SettingsFacade,
    private authService: AuthService
  ) {
    this.isBrowser = environment.production && this.browserService.isBrowser();

    if (this.isBrowser && this.browserService.getStorageItem('userInteractionEvents')) {
      try {

        const storedEvents = JSON.parse(this.browserService.getStorageItem('userInteractionEvents') || '[]');
        this.eventBatch = Array.isArray(storedEvents) ? storedEvents : [];
        if (this.eventBatch.length > 0)
          this.flushBatch();
      } catch (e) {
        this.logger.error('Failed to parse stored user interaction events:', e);
      }

    }

    if (this.settingService.getSetting('elasticSendIntervalInSeconds')) {
      this.BATCH_TIMEOUT = +this.settingService.getSetting('elasticSendIntervalInSeconds') * 1000;
    }
    if (this.settingService.getSetting('maxElasticBufferSize')) {
      this.BATCH_SIZE = +this.settingService.getSetting('maxElasticBufferSize');
    }
    const eventsToLog = this.settingService.getSetting('eventsToLogToElastic');

    if (Array.isArray(eventsToLog)) {
      this.targetEvens = eventsToLog;
    }

  }

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json'
    });
  }

  private addToBatch(event: UserInteractionData): void {
    // Only batch events in browser environment
    if (!this.isBrowser) {
      return;
    }

    this.eventBatch.push(event);

    this.browserService.setStorageItem('userInteractionEvents', JSON.stringify(this.eventBatch));
    // Start timer if this is the first event in the batch
    if (this.eventBatch.length === 1) {
      this.startBatchTimer();
    }

    // Send immediately if batch is full
    if (this.eventBatch.length >= this.BATCH_SIZE) {
      this.flushBatch();
    }
  }

  private startBatchTimer(): void {
    if (!this.isBrowser) {
      return;
    }

    this.clearBatchTimer();

    this.batchTimer = timer(this.BATCH_TIMEOUT)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.eventBatch.length > 0) {
          this.flushBatch();
        }
      });
  }

  private clearBatchTimer(): void {
    if (this.batchTimer) {
      this.batchTimer.unsubscribe();
      this.batchTimer = null;
    }
  }

  private flushBatch(): void {
    if (!this.isBrowser || this.eventBatch.length === 0) {
      return;
    }

    const batchToSend = [...this.eventBatch];
    this.clearBatchTimer();

    this.logger.log(`Sending batch of ${batchToSend.length} events to Azure Function`);

    this.sendBatch(batchToSend).subscribe({
      next: (response) => {
        this.eventBatch = [];
        this.browserService.removeStorageItem('userInteractionEvents');
        this.logger.log(`Successfully sent ${batchToSend.length} events to Azure Function`, response);
      },
      error: (error) => {
        this.logger.error(`Failed to send batch of ${batchToSend.length} events:`, error);
        // Optionally, you could implement retry logic here
      }
    });
  }

  private sendBatch(batch: UserInteractionData[]): Observable<any> {
    if (!this.isBrowser) {
      return of(null);
    }

    return this.http.post(this.userInteractionUrl, batch, { headers: this.getHeaders() }).pipe(
      catchError(error => {
        this.logger.error('Failed to send batch to Azure Function:', error);
        return of(null);
      })
    );
  }

  logUserAction(eventData: any): Observable<any> {

    if (!this.isBrowser) {
      return of({ success: false, message: 'SSR - Event not logged' });
    }



    if (eventData.event && !this.targetEvens.includes(eventData.event)) {
      return of({ success: false, message: 'Event not logged - not a target event' });
    }

    const timestamp = new Date().toISOString();



    const userInteractionData: UserInteractionData = {
      listings_id: eventData.listing_id || 'unknown',
      user_id: this.authService.userValue?.id || 'anonymous',
      transction_date: timestamp,
      listing_name: eventData.listing_name || 'Unknown',
      category_name: eventData.category_name || 'Unknown',
      user_action: eventData.event,
      request_source: this.dvService.isMobile ? 'web_mobile' : 'web_desktop',
      platform: 'web',
      seller_id: eventData.seller_id || 'unknown',
    };

    // Add to batch instead of sending immediately
    this.addToBatch(userInteractionData);

    // Return a resolved observable for compatibility
    return of({ success: true, message: 'Event added to batch' });
  }



  // Method to manually flush the batch if needed
  flushEvents(): void {
    this.flushBatch();
  }

  // Method to get current batch status (useful for debugging)
  getBatchStatus(): { count: number; maxSize: number; timeoutMs: number } {
    return {
      count: this.eventBatch.length,
      maxSize: this.BATCH_SIZE,
      timeoutMs: this.BATCH_TIMEOUT
    };
  }
}
