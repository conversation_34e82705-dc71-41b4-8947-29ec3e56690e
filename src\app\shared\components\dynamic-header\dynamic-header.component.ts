import { ChangeDetectionStrategy, Component, ContentChild, ElementRef, EventEmitter, Input, Output } from '@angular/core';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { NgIf } from '@angular/common';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { AdsAndAddOnsService } from '@src/app/modules/ads-and-add-ons/services/ads-and-add-ons.service';

@Component({
  selector: 'app-dynamic-header',
  standalone: true,
  imports: [SvgIconsComponent, NgIf],
  templateUrl: './dynamic-header.component.html',
  styleUrl: './dynamic-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DynamicHeaderComponent {
  @Input() title: string = '';
  @Input() showBackButton: boolean = true;
  @Input() showFilterButton: boolean = false;

  @Output() backButtonClicked = new EventEmitter<void>();
  @Output() filterButtonClicked = new EventEmitter<void>();

  @ContentChild('[header-filter]') filterSlot?: ElementRef;

  hasFilterSlot = false;

  constructor(private alertHandlerService: AlertHandlerService, private adsAndAddOnsService: AdsAndAddOnsService) { }

  ngAfterContentInit() {
    this.hasFilterSlot = !!this.filterSlot;
  }

  onBackButtonClick() {
    this.backButtonClicked.emit();
  }

  onFilterButtonClick() {
    this.filterButtonClicked.emit();
  }

  onCloseButtonClick() {

    if (this.alertHandlerService.ref) {
      this.alertHandlerService.ref.close();
      this.adsAndAddOnsService.reset();
    }
  }

}
