import { Component } from '@angular/core';
import { AdAddonsDetailsComponent } from "../components/ad-addons-details/ad-addons-details.component";
import { CommonModule } from '@angular/common';
import { AdAddonsPickerComponent } from '../components/ad-addons-picker/ad-addons-picker.component';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';
import { AdsAndAddOnsService, AdsStage } from '../services/ads-and-add-ons.service';

@Component({
  selector: 'app-ads-and-add-ons-sheet-cycle',
  standalone: true,
  imports: [AdAddonsDetailsComponent, CommonModule, AdAddonsPickerComponent, DynamicHeaderComponent],
  templateUrl: './ads-and-add-ons-sheet-cycle.component.html',
  styleUrl: './ads-and-add-ons-sheet-cycle.component.scss'
})
export class AdsAndAddOnsSheetCycleComponent {

  AdsStage = AdsStage;

  constructor(public svc: AdsAndAddOnsService) { }

}
