.quick-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
}

.quick-buttons button {
    padding: 0.2rem 1rem;
    border-radius: 6px;
    border: 1px solid #d2a8df;
    background-color: white;
    color: #7a1d8e;
    font-weight: 500;
    cursor: pointer;
    font-family: inherit;
    transition: all 0.2s ease;
}

.quick-buttons button.active {
    background-color: #e9d7f1;
    border: 1px solid transparent;
    font-weight: bold;
}

::ng-deep .p-datepicker {
    border: none !important;
    width: 100% !important;
}

::ng-deep .p-calendar {
    border: none !important;
    width: 100% !important;
}

/* Base highlight style on the <td> */
::ng-deep .p-datepicker td:has(> span.p-highlight) {
    background: #b288c0 !important;
    color: white !important;
    border-radius: 0 !important;
}

/* Reset span background */
::ng-deep .p-datepicker td>span.p-highlight {
    background: transparent !important;
    color: inherit !important;
}

/* Remove border-radius from all highlighted cells first */
::ng-deep .p-datepicker td:has(> span.p-highlight.p-datepicker-current-day) {
    border-radius: 0 !important;
}

/* first cell in the row that has a highlight */
::ng-deep .p-datepicker tr td:has(> span.p-highlight.p-datepicker-current-day):first-child {
    /* top+bottom inline-start corners (left in LTR, right in RTL) */
    border-start-start-radius: 6px !important;
    border-end-start-radius: 6px !important;
}

/* last cell in the row that has a highlight */
::ng-deep .p-datepicker tr td:has(> span.p-highlight.p-datepicker-current-day):last-child {
    /* top+bottom inline-end corners (right in LTR, left in RTL) */
    border-start-end-radius: 6px !important;
    border-end-end-radius: 6px !important;
}

/* this td is highlighted, next td is NOT → end of run  */
::ng-deep .p-datepicker tr td:has(> span.p-highlight.p-datepicker-current-day):not(:has(+ td > span.p-highlight)) {
    border-start-end-radius: 6px !important;
    border-end-end-radius: 6px !important;
}

/* previous td is NOT highlighted, this td IS → start of run */
::ng-deep .p-datepicker tr td:not(:has(span.p-highlight.p-datepicker-current-day))+td:has(> span.p-highlight.p-datepicker-current-day) {
    border-start-start-radius: 6px !important;
    border-end-start-radius: 6px !important;
}