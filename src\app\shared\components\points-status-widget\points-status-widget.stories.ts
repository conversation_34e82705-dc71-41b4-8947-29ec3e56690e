// points-status-widget.stories.ts
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { PointsStatusWidgetComponent } from './points-status-widget.component';

const meta: Meta<PointsStatusWidgetComponent> = {
    title: 'shared/Points Status Widget',
    component: PointsStatusWidgetComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, PointsStatusWidgetComponent],
        }),
    ],
    argTypes: {
        current: { control: { type: 'number', min: 0 } },
        total: { control: { type: 'number', min: 1 } },
        pointsRequired: { control: { type: 'number', min: 0 } },
        expiresAt: { control: 'date' },
    },
    tags: ['autodocs'],
};
export default meta;

type Story = StoryObj<PointsStatusWidgetComponent>;

export const Default: Story = {
    args: {
        current: 22,
        total: 100,
        pointsRequired: 10,
        expiresAt: new Date('2025-02-11'),
    },
};

export const EnoughPoints: Story = {
    args: {
        current: 750,
        total: 1000,
        pointsRequired: 200, // can buy → bar stays normal
        expiresAt: new Date('2025-12-31'),
    },
};

export const NotEnoughPoints: Story = {
    args: {
        current: 1000,
        total: 1000,
        pointsRequired: 4000, // cannot buy → bar + count turn red, details show
        expiresAt: new Date('2025-02-11'),
    },
};

export const NoRequiredCost: Story = {
    args: {
        current: 340,
        total: 1000,
        pointsRequired: 0, // hides the "will-fill" overlay
        expiresAt: new Date(),
    },
};
