import { Component, Input } from '@angular/core';
import { LazyloadDirective } from '../../directives/lazyload.directive';
import { ListingImagePathPipe } from '../../pipes/listing-image-path.pipe';
import { ListingSlideImage } from './listing-slide.model';

@Component({
  selector: 'app-listing-slide-image',
  standalone: true,
  imports: [LazyloadDirective, ListingImagePathPipe],
  templateUrl: './listing-slide-image.component.html',
  styleUrl: './listing-slide-image.component.scss'
})
export class ListingSlideImageComponent {

  @Input() image: ListingSlideImage;

}
