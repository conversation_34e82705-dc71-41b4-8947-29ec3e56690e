import { HttpClient } from "@angular/common/http";
import { Injectable, signal } from "@angular/core";
import { environment } from 'src/environments/environment';
import { PackagePlan } from "../models/packages.model";
@Injectable({
    providedIn: 'root'
})
export class PackagesService {

    constructor(private http: HttpClient) {}
    private readonly _plans = signal<PackagePlan[] | null>(null);
    public readonly plans = this._plans.asReadonly();

    getPackagePlans() {
        return this.http.get<PackagePlan[]>(`${environment.authAPI}/api/Subscription/plans`);
    }
    getPackagePlanById(id: number) {
        return this.http.get<PackagePlan>(`${environment.authAPI}/api/Subscription/plan?id=${id}`);
    }
    
}