import { HttpClient } from "@angular/common/http";
import { computed, Injectable, signal } from "@angular/core";
import { environment } from 'src/environments/environment';
import { PackagePlan } from "../models/packages.model";
import { tap } from "rxjs";
@Injectable({
    providedIn: 'root'
})
export class PackagesService {

    constructor(private http: HttpClient) {}
    private readonly _plans = signal<PackagePlan[] | null>(null);
    public readonly plans = this._plans.asReadonly();
    planById = (id: number) => computed(() => this._plans()?.find(p => p.id === id));

    getPackagePlans() {
        return this.http.get<PackagePlan[]>(`${environment.authAPI}/api/Subscription/plans`).pipe(tap(data => this.setPlans(data)));
    }
    currentPlans() {
        this._plans.set(this.plans());
    }
    setPlans(plans: PackagePlan[]) {
        this._plans.set(plans);
    }
}