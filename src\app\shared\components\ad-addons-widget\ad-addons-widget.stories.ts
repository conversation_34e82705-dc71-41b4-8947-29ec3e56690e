import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';

import { AdAddonsWidgetComponent, AddonOption } from './ad-addons-widget.component';
import { SvgIconComponent } from '@src/app/core/components/svg-icon.component';
import { SharedBtnComponent } from '../shared-btn/shared-btn.component';

const meta: Meta<AdAddonsWidgetComponent> = {
    title: 'shared/Ad Addons Widget',
    component: AdAddonsWidgetComponent,
    decorators: [
        moduleMetadata({
            imports: [SvgIconComponent, SharedBtnComponent],
        }),
    ],
    argTypes: {
        readMore: { action: 'readMore clicked' },
        add: { action: 'add clicked' },
    },
    tags: ['autodocs'],
};
export default meta;

type Story = StoryObj<AdAddonsWidgetComponent>;

const bumpAddon: AddonOption = {
    id: 'bump',
    title: '<PERSON> Bump',
    iconId: '<PERSON> Bump',
    description: 'تدوير الإعلان وإظهاره بشكل بارز في الصفحة الرئيسية، صفحة الفئة، ونتائج البحث.',
    validityValue: '7',
    costValue: '200',
    subscribed: false,
};

const featuredAddon: AddonOption = {
    id: 'topFeatured',
    title: 'Top Featured Ad',
    iconId: 'Top Featured Ad',
    description: 'يتم عرض الإعلان مع شارة مميزة بشكل بارز في الصفحة الرئيسية، صفحة الفئة، ونتائج البحث.',
    validityValue: '7',
    costValue: '300',
    subscribed: true,
    daysLeft: 4,
};

export const NotSubscribed: Story = {
    args: {
        addon: bumpAddon,
        disabled: false,
        showReadMore: true,
        reviewMode: false,
    },
};

export const viewMode: Story = {
    args: {
        addon: bumpAddon,
        disabled: false,
        showReadMore: true,
        reviewMode: true,
    },
};

export const Subscribed: Story = {
    args: {
        addon: featuredAddon,
        disabled: false,
        showReadMore: true,
        reviewMode: false,
    },
};

export const DisabledState: Story = {
    args: {
        addon: bumpAddon,
        disabled: true,
        showReadMore: true,
        reviewMode: false,
    },
};
